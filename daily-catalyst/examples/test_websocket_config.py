#!/usr/bin/env python3
"""
Test WebSocket Configuration

Simple test to verify WebSocket and async configuration settings.
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.config_loader import get_config_loader

load_dotenv()

async def test_websocket_config():
    """Test WebSocket configuration settings"""
    print("🧪 Testing WebSocket Configuration")
    print("=" * 50)

    # Load configuration
    config_loader = get_config_loader()
    config = config_loader.load_all_configs()

    if not config.is_valid:
        print("❌ Configuration has errors:")
        for error in config.errors:
            print(f"   - {error}")
        return False

    # Test WebSocket settings
    print("\n📡 WebSocket Settings:")
    websocket_enabled = config_loader.get_setting('api.deepgram.websocket.enable_direct_websocket', False)
    print(f"   ✅ Direct WebSocket enabled: {websocket_enabled}")

    url_template = config_loader.get_setting('api.deepgram.websocket.url_template', '')
    print(f"   ✅ URL template configured: {len(url_template) > 0}")
    if url_template:
        print(f"      Template: {url_template[:80]}...")

    auth_header = config_loader.get_setting('api.deepgram.websocket.auth_header', '')
    print(f"   ✅ Auth header template: {len(auth_header) > 0}")

    # Test model settings
    print("\n🤖 Model Settings:")
    model = config_loader.get_setting('api.deepgram.model', 'nova-3')
    print(f"   ✅ Deepgram model: {model}")

    # Test async settings
    print("\n⚡ Async Processing Settings:")
    async_enabled = config_loader.get_setting('performance.async_processing.enable_async_mode', False)
    print(f"   ✅ Async mode enabled: {async_enabled}")

    queue_size = config_loader.get_setting('performance.async_processing.queue_size', 1000)
    print(f"   ✅ Queue size: {queue_size}")

    task_timeout = config_loader.get_setting('performance.async_processing.task_timeout', 30)
    print(f"   ✅ Task timeout: {task_timeout}s")

    concurrent_tasks = config_loader.get_setting('performance.async_processing.concurrent_tasks', 3)
    print(f"   ✅ Concurrent tasks: {concurrent_tasks}")

    # Test microphone settings
    print("\n🎤 Microphone Settings:")
    mic_enabled = config_loader.get_setting('audio.source.microphone.enable_live_mic', False)
    print(f"   ✅ Live microphone: {mic_enabled}")

    chunk_size = config_loader.get_setting('audio.source.microphone.chunk_size', 8000)
    print(f"   ✅ Microphone chunk size: {chunk_size}")

    mic_format = config_loader.get_setting('audio.source.microphone.format', 'paInt16')
    print(f"   ✅ Audio format: {mic_format}")

    # Test speed settings
    print("\n🏃 Speed Settings:")
    speed_multiplier = config_loader.get_setting('audio.streaming.speed_multiplier', 1.0)
    print(f"   ✅ Speed multiplier: {speed_multiplier}x")

    chunk_duration = config_loader.get_setting('audio.streaming.chunk_duration_ms', 250)
    print(f"   ✅ Chunk duration: {chunk_duration}ms")

    # Test speed profiles
    speed_profiles = config_loader.get_setting('audio.streaming.speed_profiles', {})
    print(f"   ✅ Speed profiles available: {len(speed_profiles)}")
    for name, value in speed_profiles.items():
        current = " (current)" if value == speed_multiplier else ""
        print(f"      - {name}: {value}x{current}")

    return True

async def test_websocket_url_building():
    """Test WebSocket URL building"""
    print("\n🔗 Testing WebSocket URL Building")
    print("=" * 50)

    config_loader = get_config_loader()

    # Get settings
    url_template = config_loader.get_setting('api.deepgram.websocket.url_template', '')
    model = config_loader.get_setting('api.deepgram.model', 'nova-3')

    if url_template:
        try:
            # Build URL
            websocket_url = url_template.format(model=model)
            print(f"✅ WebSocket URL: {websocket_url}")

            # Parse URL components
            if 'wss://api.deepgram.com' in websocket_url:
                print("✅ Valid Deepgram WebSocket endpoint")

            if f'model={model}' in websocket_url:
                print(f"✅ Model parameter included: {model}")

            if 'punctuate=true' in websocket_url:
                print("✅ Punctuation enabled")

            if 'diarize=true' in websocket_url:
                print("✅ Speaker diarization enabled")

            return True

        except Exception as e:
            print(f"❌ URL building failed: {e}")
            return False
    else:
        print("❌ No URL template configured")
        return False

async def test_async_capabilities():
    """Test async processing capabilities"""
    print("\n⚡ Testing Async Capabilities")
    print("=" * 50)

    config_loader = get_config_loader()

    # Test async queue simulation
    queue_size = config_loader.get_setting('performance.async_processing.queue_size', 1000)
    test_queue = asyncio.Queue(maxsize=queue_size)

    print(f"✅ Created async queue with size: {queue_size}")

    # Test concurrent task simulation
    async def mock_sender():
        await asyncio.sleep(0.1)
        return "sender_done"

    async def mock_receiver():
        await asyncio.sleep(0.1)
        return "receiver_done"

    async def mock_processor():
        await asyncio.sleep(0.1)
        return "processor_done"

    try:
        # Run concurrent tasks
        tasks = [
            asyncio.create_task(mock_sender()),
            asyncio.create_task(mock_receiver()),
            asyncio.create_task(mock_processor()),
        ]

        results = await asyncio.gather(*tasks)
        print(f"✅ Concurrent tasks completed: {results}")

        return True

    except Exception as e:
        print(f"❌ Async test failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🎯 Daily Catalyst - WebSocket & Async Configuration Test")
    print("📚 Testing configuration system enhancements")
    print()

    # Check API key
    api_key = os.getenv("DEEPGRAM_API_KEY")
    if not api_key:
        print("⚠️ DEEPGRAM_API_KEY not set (required for actual WebSocket connection)")
    else:
        print("✅ DEEPGRAM_API_KEY found")

    print()

    # Run tests
    tests = [
        ("Configuration Loading", test_websocket_config()),
        ("WebSocket URL Building", test_websocket_url_building()),
        ("Async Capabilities", test_async_capabilities()),
    ]

    results = []
    for test_name, test_coro in tests:
        try:
            result = await test_coro
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
        print()

    # Summary
    print("📊 Test Results Summary")
    print("=" * 50)
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1

    print(f"\n🎯 {passed}/{len(results)} tests passed")

    if passed == len(results):
        print("🎉 All tests passed! WebSocket and async configuration is ready.")
    else:
        print("⚠️ Some tests failed. Check configuration settings.")

if __name__ == "__main__":
    asyncio.run(main())