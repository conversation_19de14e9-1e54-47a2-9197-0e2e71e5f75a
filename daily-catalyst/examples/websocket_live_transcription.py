#!/usr/bin/env python3
"""
WebSocket Live Transcription Example

This example demonstrates direct WebSocket connection to Deepgram
inspired by the AI Scribe pattern, adapted for daily-catalyst.

Usage:
    python examples/websocket_live_transcription.py
"""

import asyncio
import json
import os
import sys
import websockets
from datetime import datetime
from dotenv import load_dotenv

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.config_loader import get_config_loader

# Load environment variables
load_dotenv()

# Global variables
audio_queue = asyncio.Queue()
all_transcripts = []

class WebSocketTranscriber:
    """
    Direct WebSocket transcriber using Deepgram API
    Inspired by AI Scribe example but adapted for daily-catalyst
    """

    def __init__(self, config_loader=None):
        if config_loader is None:
            config_loader = get_config_loader()
        self.config_loader = config_loader
        self.config = config_loader.load_all_configs()

        # Get WebSocket settings
        self.websocket_enabled = config_loader.get_setting('api.deepgram.websocket.enable_direct_websocket', False)
        self.url_template = config_loader.get_setting(
            'api.deepgram.websocket.url_template',
            'wss://api.deepgram.com/v1/listen?punctuate=true&diarize=true&model={model}&encoding=linear16&sample_rate=16000'
        )

        # Get model settings
        self.model = config_loader.get_setting('api.deepgram.model', 'nova-3')

        # Get async settings
        self.queue_size = config_loader.get_setting('performance.async_processing.queue_size', 1000)
        self.task_timeout = config_loader.get_setting('performance.async_processing.task_timeout', 30)

    def get_websocket_url(self, api_key: str) -> str:
        """Build WebSocket URL with parameters"""
        return self.url_template.format(model=self.model)

    def get_auth_headers(self, api_key: str) -> dict:
        """Get authentication headers"""
        return {"Authorization": f"Token {api_key}"}

    async def sender(self, ws, audio_queue):
        """Send audio data to Deepgram WebSocket"""
        print("🟢 Ready to stream audio to Deepgram")
        try:
            while True:
                # In real implementation, this would get audio from microphone or file
                # For demo, we'll simulate with empty data
                await asyncio.sleep(0.1)  # Simulate audio capture delay

                # Example: mic_data = await audio_queue.get()
                # await ws.send(mic_data)

        except asyncio.CancelledError:
            return
        except websockets.exceptions.ConnectionClosedOK:
            await ws.send(json.dumps({"type": "CloseStream"}))
            print("🟢 Successfully closed Deepgram connection")
        except Exception as e:
            print(f"Error while sending: {str(e)}")
            raise

    async def receiver(self, ws):
        """Receive transcription results from Deepgram"""
        first_message = True

        async for msg in ws:
            res = json.loads(msg)
            if first_message:
                print("🟢 Successfully receiving Deepgram messages")
                first_message = False

            try:
                if res.get("is_final"):
                    transcript = self.get_speaker_transcripts(res)
                    if transcript:
                        print(f"📝 {transcript}")
                        all_transcripts.append(transcript)

                        # Analyze with off-topic detection (daily-catalyst feature)
                        await self.analyze_transcript(transcript)

            except KeyError:
                print(f"🔴 ERROR: Received unexpected API response! {msg}")

    def get_speaker_transcripts(self, json_data):
        """Parse speaker transcripts from Deepgram response"""
        speaker_transcripts = {}
        channel = json_data.get("channel", {})
        alternatives = channel.get("alternatives", [])

        for alternative in alternatives:
            for word_info in alternative.get("words", []):
                speaker_id = word_info.get("speaker", "Unknown")
                punctuated_word = word_info.get("punctuated_word", word_info.get("word", ""))
                if speaker_id not in speaker_transcripts:
                    speaker_transcripts[speaker_id] = []
                speaker_transcripts[speaker_id].append(punctuated_word)

        formatted_transcripts = []
        for speaker_id, words in speaker_transcripts.items():
            formatted_transcripts.append(f"Speaker {speaker_id}: {' '.join(words)}")

        return "\n".join(formatted_transcripts)

    async def analyze_transcript(self, transcript):
        """Analyze transcript for off-topic content (daily-catalyst feature)"""
        try:
            # This would integrate with RealTimeOffTopicAnalyzer
            # For demo, just show the concept
            if len(transcript) > 100:  # Simple length check
                print("🤖 AI Analysis: Long segment detected, analyzing...")
                # Here would be the actual off-topic analysis

        except Exception as e:
            print(f"Analysis error: {e}")

    async def run(self, api_key: str):
        """Main WebSocket connection and processing loop"""
        if not self.websocket_enabled:
            print("❌ WebSocket mode not enabled in configuration")
            print("   Set api.deepgram.websocket.enable_direct_websocket: true")
            return

        websocket_url = self.get_websocket_url(api_key)
        headers = self.get_auth_headers(api_key)

        print(f"🔗 Connecting to: {websocket_url}")

        try:
            # Try with extra_headers first, fallback to older websockets API
            try:
                async with websockets.connect(websocket_url, extra_headers=headers) as ws:
                    await self._run_websocket_session(ws)
            except TypeError:
                # Fallback for older websockets versions
                print("🔄 Using fallback WebSocket connection method...")
                async with websockets.connect(websocket_url) as ws:
                    # Send auth as first message for older versions
                    await ws.send(json.dumps({"type": "auth", "token": headers["Authorization"].split(" ")[1]}))
                    await self._run_websocket_session(ws)

        except Exception as e:
            print(f"❌ WebSocket connection failed: {e}")

    async def _run_websocket_session(self, ws):
        """Run the WebSocket session with tasks"""
        print("🟢 Successfully opened Deepgram streaming connection")

        tasks = [
            asyncio.ensure_future(self.sender(ws, audio_queue)),
            asyncio.ensure_future(self.receiver(ws)),
            # asyncio.ensure_future(self.microphone(audio_queue)),  # Would be implemented for real mic
        ]

        try:
            await asyncio.wait_for(asyncio.gather(*tasks), timeout=self.task_timeout)
        except asyncio.TimeoutError:
            print("⏰ Session timeout reached")
        except asyncio.CancelledError:
            print("🛑 Session cancelled")


async def main():
    """Main function"""
    api_key = os.getenv("DEEPGRAM_API_KEY")
    if not api_key:
        print("❌ Please set the DEEPGRAM_API_KEY environment variable.")
        sys.exit(1)

    print("🎯 Daily Catalyst - WebSocket Live Transcription Example")
    print("📚 Inspired by AI Scribe pattern with daily-catalyst features")
    print()

    # Load configuration
    config_loader = get_config_loader()
    config = config_loader.load_all_configs()

    if not config.is_valid:
        print("⚠️ Configuration has errors:")
        for error in config.errors:
            print(f"   - {error}")

    # Create and run transcriber
    transcriber = WebSocketTranscriber(config_loader)

    try:
        await transcriber.run(api_key)
    except KeyboardInterrupt:
        print("\n🛑 Stopped by user")
    except Exception as e:
        print(f"❌ Error: {e}")

    print(f"📊 Total transcripts captured: {len(all_transcripts)}")


if __name__ == "__main__":
    asyncio.run(main())