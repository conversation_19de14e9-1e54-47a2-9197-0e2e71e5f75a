#!/usr/bin/env python3
"""
Configuration Loader for Daily Catalyst

This module provides centralized configuration loading with validation,
fallback support, and error handling for the daily-catalyst project.
"""

import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class ConfigPaths:
    """Configuration file paths"""
    prompts: str = "config/prompts.yaml"
    settings: str = "config/settings.yaml"
    main_config: str = "config.yaml"
    custom_prompts_dir: str = "config/custom_prompts"


@dataclass
class LoadedConfig:
    """Container for all loaded configuration"""
    prompts: Dict[str, Any] = field(default_factory=dict)
    settings: Dict[str, Any] = field(default_factory=dict)
    main_config: Dict[str, Any] = field(default_factory=dict)
    is_valid: bool = True
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)


class ConfigurationError(Exception):
    """Raised when configuration loading fails"""
    pass


class ConfigLoader:
    """
    Centralized configuration loader with validation and fallback support
    """

    def __init__(self, config_root: Optional[str] = None):
        """
        Initialize configuration loader

        Args:
            config_root: Root directory for configuration files (default: current directory)
        """
        self.config_root = Path(config_root) if config_root else Path.cwd()
        self.paths = ConfigPaths()
        self._cache = {}

    def load_all_configs(self, use_cache: bool = True) -> LoadedConfig:
        """
        Load all configuration files

        Args:
            use_cache: Whether to use cached configurations

        Returns:
            LoadedConfig object with all loaded configurations
        """
        if use_cache and 'all_configs' in self._cache:
            return self._cache['all_configs']

        config = LoadedConfig()

        try:
            # Load main configuration (existing config.yaml)
            config.main_config = self._load_yaml_file(self.paths.main_config, required=False)

            # Load prompts configuration
            config.prompts = self._load_yaml_file(self.paths.prompts, required=True)

            # Load settings configuration
            config.settings = self._load_yaml_file(self.paths.settings, required=True)

            # Validate configurations
            self._validate_config(config)

            # Load custom prompts if available
            self._load_custom_prompts(config)

        except Exception as e:
            config.is_valid = False
            config.errors.append(f"Failed to load configurations: {str(e)}")
            logger.error(f"Configuration loading failed: {e}")

        if use_cache:
            self._cache['all_configs'] = config

        return config

    def _load_yaml_file(self, file_path: str, required: bool = True) -> Dict[str, Any]:
        """
        Load a YAML file with error handling

        Args:
            file_path: Path to YAML file relative to config_root
            required: Whether the file is required to exist

        Returns:
            Loaded YAML data as dictionary
        """
        full_path = self.config_root / file_path

        if not full_path.exists():
            if required:
                raise ConfigurationError(f"Required configuration file not found: {file_path}")
            else:
                logger.warning(f"Optional configuration file not found: {file_path}")
                return {}

        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f) or {}
                logger.info(f"Loaded configuration from {file_path}")
                return data

        except yaml.YAMLError as e:
            raise ConfigurationError(f"Invalid YAML in {file_path}: {str(e)}")
        except Exception as e:
            raise ConfigurationError(f"Failed to read {file_path}: {str(e)}")

    def _validate_config(self, config: LoadedConfig) -> None:
        """
        Validate loaded configurations

        Args:
            config: LoadedConfig object to validate
        """
        # Validate prompts configuration
        self._validate_prompts(config)

        # Validate settings configuration
        self._validate_settings(config)

        # Cross-validate configurations
        self._cross_validate(config)

    def _validate_prompts(self, config: LoadedConfig) -> None:
        """Validate prompts configuration"""
        prompts = config.prompts

        # Check required sections
        required_sections = ['off_topic_detection', 'prompt_versions']
        for section in required_sections:
            if section not in prompts:
                config.errors.append(f"Missing required prompts section: {section}")

        # Validate main prompt
        if 'off_topic_detection' in prompts:
            off_topic = prompts['off_topic_detection']

            if 'realtime_prompt' not in off_topic:
                config.errors.append("Missing realtime_prompt in off_topic_detection")
            else:
                prompt = off_topic['realtime_prompt']
                required_placeholders = ['{context}', '{new_phrase}']
                for placeholder in required_placeholders:
                    if placeholder not in prompt:
                        config.errors.append(f"Missing placeholder {placeholder} in realtime_prompt")

        # Validate prompt versions
        if 'prompt_versions' in prompts:
            versions = prompts['prompt_versions']
            if 'current_version' not in versions:
                config.warnings.append("No current_version specified in prompt_versions")

    def _validate_settings(self, config: LoadedConfig) -> None:
        """Validate settings configuration"""
        settings = config.settings

        # Check required sections
        required_sections = ['api', 'audio', 'analysis']
        for section in required_sections:
            if section not in settings:
                config.errors.append(f"Missing required settings section: {section}")

        # Validate API settings
        if 'api' in settings:
            api = settings['api']

            # Validate Gemini models
            if 'gemini' in api and 'models' in api['gemini']:
                models = api['gemini']['models']
                if not models or not isinstance(models, list):
                    config.errors.append("Gemini models must be a non-empty list")
                else:
                    for i, model in enumerate(models):
                        required_fields = ['name', 'display_name', 'rpm_limit', 'daily_limit']
                        for field in required_fields:
                            if field not in model:
                                config.errors.append(f"Missing field '{field}' in gemini model {i}")

    def _cross_validate(self, config: LoadedConfig) -> None:
        """Cross-validate configurations between files"""
        # Check if current prompt version exists
        if ('prompt_versions' in config.prompts and
            'current_version' in config.prompts['prompt_versions']):

            current_version = config.prompts['prompt_versions']['current_version']
            off_topic = config.prompts.get('off_topic_detection', {})

            # Check if current version exists in main prompts or alternatives
            if (current_version not in off_topic and
                'alternative_prompts' in off_topic and
                current_version not in off_topic['alternative_prompts']):
                config.warnings.append(f"Current prompt version '{current_version}' not found")

    def _load_custom_prompts(self, config: LoadedConfig) -> None:
        """Load custom prompts from custom directory"""
        custom_dir = self.config_root / self.paths.custom_prompts_dir

        if not custom_dir.exists():
            return

        try:
            for prompt_file in custom_dir.glob("*.yaml"):
                try:
                    custom_prompts = self._load_yaml_file(
                        str(prompt_file.relative_to(self.config_root)),
                        required=False
                    )

                    # Merge custom prompts into main prompts
                    if 'off_topic_detection' not in config.prompts:
                        config.prompts['off_topic_detection'] = {}
                    if 'alternative_prompts' not in config.prompts['off_topic_detection']:
                        config.prompts['off_topic_detection']['alternative_prompts'] = {}

                    config.prompts['off_topic_detection']['alternative_prompts'].update(custom_prompts)
                    config.warnings.append(f"Loaded custom prompts from {prompt_file.name}")

                except Exception as e:
                    config.warnings.append(f"Failed to load custom prompts from {prompt_file.name}: {str(e)}")

        except Exception as e:
            config.warnings.append(f"Error scanning custom prompts directory: {str(e)}")

    def get_prompt(self, prompt_type: str = "realtime_prompt", config: Optional[LoadedConfig] = None) -> str:
        """
        Get a specific prompt with fallback support

        Args:
            prompt_type: Type of prompt to retrieve
            config: LoadedConfig object (loads if not provided)

        Returns:
            Prompt string
        """
        if config is None:
            config = self.load_all_configs()

        if not config.is_valid:
            raise ConfigurationError("Cannot get prompt from invalid configuration")

        # Try to get from current version setting
        if 'prompt_versions' in config.prompts:
            current_version = config.prompts['prompt_versions'].get('current_version', prompt_type)
            if current_version != prompt_type:
                prompt_type = current_version

        # Look in main prompts first
        off_topic = config.prompts.get('off_topic_detection', {})
        if prompt_type in off_topic:
            return off_topic[prompt_type]

        # Look in alternative prompts
        if 'alternative_prompts' in off_topic and prompt_type in off_topic['alternative_prompts']:
            return off_topic['alternative_prompts'][prompt_type]

        # Fallback to default
        if 'realtime_prompt' in off_topic:
            logger.warning(f"Prompt type '{prompt_type}' not found, using default realtime_prompt")
            return off_topic['realtime_prompt']

        raise ConfigurationError(f"No prompt found for type: {prompt_type}")

    def get_setting(self, setting_path: str, default: Any = None, config: Optional[LoadedConfig] = None) -> Any:
        """
        Get a setting value using dot notation path

        Args:
            setting_path: Dot-separated path to setting (e.g., "api.gemini.models")
            default: Default value if setting not found
            config: LoadedConfig object (loads if not provided)

        Returns:
            Setting value or default
        """
        if config is None:
            config = self.load_all_configs()

        if not config.is_valid:
            logger.warning("Using default value due to invalid configuration")
            return default

        # Navigate through nested dictionary using dot notation
        current = config.settings
        for key in setting_path.split('.'):
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return default

        return current

    def clear_cache(self) -> None:
        """Clear configuration cache"""
        self._cache.clear()
        logger.info("Configuration cache cleared")


# Global configuration loader instance
_config_loader = None


def get_config_loader(config_root: Optional[str] = None) -> ConfigLoader:
    """
    Get global configuration loader instance

    Args:
        config_root: Root directory for configuration files

    Returns:
        ConfigLoader instance
    """
    global _config_loader
    if _config_loader is None or config_root is not None:
        _config_loader = ConfigLoader(config_root)
    return _config_loader


def load_config(use_cache: bool = True) -> LoadedConfig:
    """
    Convenience function to load all configurations

    Args:
        use_cache: Whether to use cached configurations

    Returns:
        LoadedConfig object
    """
    return get_config_loader().load_all_configs(use_cache)


def get_prompt(prompt_type: str = "realtime_prompt") -> str:
    """
    Convenience function to get a prompt

    Args:
        prompt_type: Type of prompt to retrieve

    Returns:
        Prompt string
    """
    return get_config_loader().get_prompt(prompt_type)


def get_setting(setting_path: str, default: Any = None) -> Any:
    """
    Convenience function to get a setting

    Args:
        setting_path: Dot-separated path to setting
        default: Default value if setting not found

    Returns:
        Setting value or default
    """
    return get_config_loader().get_setting(setting_path, default)