# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Audio files
*.wav
*.mp3
*.m4a
*.flac
*.ogg

# Generated files
*.pkl
*.json
*.csv
transcripts/
analysis/
embeddings/
reports/
pretrained_models/

# PRD and planning documents (exclude from git)
prd/

# Logs
*.log
logs/

# Temporary files
tmp/
temp/
.tmp/

# Model files
models/
checkpoints/

# Data files
data/
datasets/

# Configuration overrides
config.local.yaml
.env.local

# Test files
test_data/

# Documentation build
docs/_build/
docs/build/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/
