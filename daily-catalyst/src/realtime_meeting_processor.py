#!/usr/bin/env python3
"""
Real-Time Meeting Processor for Daily Catalyst

This module integrates Deepgram STT, speaker identification, and meeting analysis
for real-time daily standup meeting processing.
"""

import asyncio
import logging
import os
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from collections import defaultdict, deque
import json

try:
    from .deepgram_stt_service import DeepgramSTTService, DeepgramTranscriptionResult
    from .realtime_audio_streamer import RealTimeAudioStreamer
    from .speaker_diarization import SpeakerDiarization
except ImportError:
    # For standalone execution
    from deepgram_stt_service import DeepgramSTTService, DeepgramTranscriptionResult
    from realtime_audio_streamer import RealTimeAudioStreamer
    from speaker_diarization import SpeakerDiarization

# Configure logging
logger = logging.getLogger(__name__)


@dataclass
class SpeakerSegment:
    """Speaker segment with timing and content"""
    speaker_id: str
    start_time: float
    end_time: float
    text: str
    confidence: float
    words: List[Dict[str, Any]] = field(default_factory=list)


@dataclass
class MeetingStats:
    """Real-time meeting statistics"""
    total_duration: float = 0.0
    speakers: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    total_words: int = 0
    total_segments: int = 0
    off_topic_segments: int = 0
    technical_deep_dives: int = 0
    last_activity: float = 0.0


class RealTimeMeetingProcessor:
    """
    Real-time meeting processor that combines:
    - Deepgram STT for transcription and diarization
    - Speaker identification and tracking
    - Meeting analysis and insights
    - Live dashboard updates
    """
    
    def __init__(self,
                 deepgram_api_key: str,
                 audio_file_path: str,
                 chunk_duration_ms: int = 100,
                 speed_multiplier: float = 1.0,
                 enable_speaker_mapping: bool = True):
        """
        Initialize real-time meeting processor.
        
        Args:
            deepgram_api_key: Deepgram API key
            audio_file_path: Path to audio file for simulation
            chunk_duration_ms: Audio chunk duration in milliseconds
            speed_multiplier: Playback speed multiplier
            enable_speaker_mapping: Enable speaker mapping to known speakers
        """
        self.deepgram_api_key = deepgram_api_key
        self.audio_file_path = audio_file_path
        self.enable_speaker_mapping = enable_speaker_mapping
        
        # Initialize services
        self.deepgram_service = DeepgramSTTService(
            api_key=deepgram_api_key,
            model="nova-3",
            language="en-US",
            enable_diarization=True,
            enable_smart_format=True,
            enable_interim_results=True
        )
        
        self.audio_streamer = RealTimeAudioStreamer(
            chunk_duration_ms=chunk_duration_ms,
            speed_multiplier=speed_multiplier
        )
        
        # Speaker system (optional)
        self.speaker_diarization = None
        if enable_speaker_mapping:
            try:
                self.speaker_diarization = SpeakerDiarization(method="simple")
            except Exception as e:
                logger.warning(f"Speaker diarization not available: {e}")
        
        # Meeting data
        self.meeting_segments: List[SpeakerSegment] = []
        self.current_transcript = ""
        self.speaker_buffer: Dict[int, List[str]] = defaultdict(list)
        self.meeting_stats = MeetingStats()
        
        # Real-time buffers
        self.recent_segments = deque(maxlen=50)  # Last 50 segments
        self.speaker_activity = defaultdict(float)  # Speaker activity tracking
        
        # Callbacks for live updates
        self.on_transcript_update = None
        self.on_speaker_change = None
        self.on_meeting_insight = None
        self.on_stats_update = None
        
        # Setup callbacks
        self._setup_callbacks()
        
        logger.info(f"RealTimeMeetingProcessor initialized for: {audio_file_path}")
    
    def _setup_callbacks(self):
        """Setup callbacks for services"""
        # Deepgram callbacks
        self.deepgram_service.on_transcript = self._handle_transcript
        self.deepgram_service.on_error = self._handle_deepgram_error
        self.deepgram_service.on_connection_opened = self._handle_connection_opened
        self.deepgram_service.on_connection_closed = self._handle_connection_closed
        
        # Audio streamer callbacks
        self.audio_streamer.on_audio_chunk = self._handle_audio_chunk
        self.audio_streamer.on_streaming_started = self._handle_streaming_started
        self.audio_streamer.on_streaming_finished = self._handle_streaming_finished
        self.audio_streamer.on_error = self._handle_streamer_error
    
    async def start_processing(self) -> bool:
        """
        Start real-time meeting processing.
        
        Returns:
            True if started successfully, False otherwise
        """
        try:
            logger.info("Starting real-time meeting processing...")
            
            # Load audio file
            if not self.audio_streamer.load_audio_file(self.audio_file_path):
                logger.error("Failed to load audio file")
                return False
            
            # Connect to Deepgram
            if not await self.deepgram_service.connect():
                logger.error("Failed to connect to Deepgram")
                return False
            
            # Start audio streaming
            if not await self.audio_streamer.start_streaming():
                logger.error("Failed to start audio streaming")
                return False
            
            logger.info("✅ Real-time meeting processing started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start processing: {e}")
            return False
    
    async def stop_processing(self):
        """Stop real-time meeting processing"""
        try:
            logger.info("Stopping real-time meeting processing...")
            
            # Stop audio streaming
            await self.audio_streamer.stop_streaming()
            
            # Disconnect from Deepgram
            await self.deepgram_service.disconnect()
            
            # Generate final meeting summary
            await self._generate_final_summary()
            
            logger.info("✅ Real-time meeting processing stopped")
            
        except Exception as e:
            logger.error(f"Error stopping processing: {e}")
    
    async def _handle_audio_chunk(self, chunk_bytes: bytes, start_time: float, end_time: float):
        """Handle audio chunk from streamer"""
        try:
            # Send audio to Deepgram
            await self.deepgram_service.send_audio(chunk_bytes)
            
        except Exception as e:
            logger.error(f"Error handling audio chunk: {e}")
    
    async def _handle_transcript(self, result: DeepgramTranscriptionResult):
        """Handle transcription result from Deepgram"""
        try:
            # Skip empty transcripts
            if not result.transcript.strip():
                return
            
            # Update meeting stats
            self.meeting_stats.last_activity = time.time()
            self.meeting_stats.total_words += len(result.transcript.split())
            
            if result.speech_final:
                self.meeting_stats.total_segments += 1
                
                # Create speaker segment
                segment = self._create_speaker_segment(result)
                self.meeting_segments.append(segment)
                self.recent_segments.append(segment)
                
                # Update speaker statistics
                self._update_speaker_stats(segment)
                
                # Analyze segment for insights
                await self._analyze_segment(segment)
                
                # Call callbacks
                if self.on_transcript_update:
                    self.on_transcript_update(segment)
                
                if self.on_stats_update:
                    self.on_stats_update(self.meeting_stats)
            
            # Log transcript
            status = "🔴 INTERIM" if not result.is_final else "🟢 FINAL"
            if result.speech_final:
                status = "🔵 SPEECH_FINAL"
            
            speakers = self._get_speakers_from_result(result)
            speaker_info = f" 👥 {speakers}" if speakers else ""
            
            logger.info(f"{status} [{result.start:.1f}s]: {result.transcript}{speaker_info}")
            
        except Exception as e:
            logger.error(f"Error handling transcript: {e}")
    
    def _create_speaker_segment(self, result: DeepgramTranscriptionResult) -> SpeakerSegment:
        """Create speaker segment from transcription result"""
        # Determine primary speaker
        speakers = self._get_speakers_from_result(result)
        primary_speaker = speakers[0] if speakers else "unknown"
        
        # Map to known speaker if available
        if self.enable_speaker_mapping and self.speaker_diarization:
            # This would integrate with your existing speaker system
            # For now, use Deepgram's speaker ID
            pass
        
        segment = SpeakerSegment(
            speaker_id=f"speaker_{primary_speaker}",
            start_time=result.start,
            end_time=result.start + result.duration,
            text=result.transcript,
            confidence=result.confidence,
            words=[{
                'word': w.word,
                'start': w.start,
                'end': w.end,
                'confidence': w.confidence,
                'speaker': w.speaker
            } for w in result.words]
        )
        
        return segment
    
    def _get_speakers_from_result(self, result: DeepgramTranscriptionResult) -> List[str]:
        """Extract unique speakers from transcription result"""
        speakers = set()
        for word in result.words:
            if word.speaker is not None:
                speakers.add(str(word.speaker))
        return sorted(list(speakers))
    
    def _update_speaker_stats(self, segment: SpeakerSegment):
        """Update speaker statistics"""
        speaker_id = segment.speaker_id
        
        if speaker_id not in self.meeting_stats.speakers:
            self.meeting_stats.speakers[speaker_id] = {
                'total_time': 0.0,
                'word_count': 0,
                'segment_count': 0,
                'avg_confidence': 0.0,
                'last_activity': 0.0
            }
        
        stats = self.meeting_stats.speakers[speaker_id]
        duration = segment.end_time - segment.start_time
        word_count = len(segment.text.split())
        
        stats['total_time'] += duration
        stats['word_count'] += word_count
        stats['segment_count'] += 1
        stats['last_activity'] = time.time()
        
        # Update average confidence
        total_confidence = stats['avg_confidence'] * (stats['segment_count'] - 1) + segment.confidence
        stats['avg_confidence'] = total_confidence / stats['segment_count']
        
        # Update speaker activity
        self.speaker_activity[speaker_id] = time.time()
    
    async def _analyze_segment(self, segment: SpeakerSegment):
        """Analyze segment for meeting insights"""
        try:
            # Simple keyword-based analysis (can be enhanced with LLM)
            text_lower = segment.text.lower()
            
            # Check for off-topic indicators
            off_topic_keywords = ['lunch', 'weekend', 'weather', 'vacation', 'personal']
            if any(keyword in text_lower for keyword in off_topic_keywords):
                self.meeting_stats.off_topic_segments += 1
                
                if self.on_meeting_insight:
                    self.on_meeting_insight({
                        'type': 'off_topic',
                        'segment': segment,
                        'message': f"Off-topic discussion detected: {segment.text[:50]}..."
                    })
            
            # Check for technical deep-dives
            technical_keywords = ['architecture', 'implementation', 'database', 'api', 'algorithm']
            if any(keyword in text_lower for keyword in technical_keywords):
                duration = segment.end_time - segment.start_time
                if duration > 30:  # More than 30 seconds
                    self.meeting_stats.technical_deep_dives += 1
                    
                    if self.on_meeting_insight:
                        self.on_meeting_insight({
                            'type': 'technical_deep_dive',
                            'segment': segment,
                            'message': f"Technical deep-dive detected ({duration:.1f}s)"
                        })
            
        except Exception as e:
            logger.error(f"Error analyzing segment: {e}")
    
    async def _generate_final_summary(self):
        """Generate final meeting summary"""
        try:
            total_duration = self.meeting_stats.total_duration
            if self.meeting_segments:
                total_duration = max(seg.end_time for seg in self.meeting_segments)
            
            summary = {
                'total_duration': total_duration,
                'total_segments': len(self.meeting_segments),
                'total_words': self.meeting_stats.total_words,
                'speakers': dict(self.meeting_stats.speakers),
                'off_topic_segments': self.meeting_stats.off_topic_segments,
                'technical_deep_dives': self.meeting_stats.technical_deep_dives,
                'segments': [
                    {
                        'speaker': seg.speaker_id,
                        'start': seg.start_time,
                        'end': seg.end_time,
                        'text': seg.text,
                        'confidence': seg.confidence
                    }
                    for seg in self.meeting_segments
                ]
            }
            
            logger.info("📊 Final Meeting Summary:")
            logger.info(f"  Duration: {total_duration:.1f}s")
            logger.info(f"  Segments: {len(self.meeting_segments)}")
            logger.info(f"  Words: {self.meeting_stats.total_words}")
            logger.info(f"  Speakers: {len(self.meeting_stats.speakers)}")
            logger.info(f"  Off-topic: {self.meeting_stats.off_topic_segments}")
            logger.info(f"  Deep-dives: {self.meeting_stats.technical_deep_dives}")
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating summary: {e}")
            return None
    
    # Event handlers
    def _handle_deepgram_error(self, error: Exception):
        logger.error(f"Deepgram error: {error}")
    
    def _handle_connection_opened(self):
        logger.info("🔗 Deepgram connection opened")
    
    def _handle_connection_closed(self):
        logger.info("🔗 Deepgram connection closed")
    
    def _handle_streaming_started(self):
        logger.info("🎵 Audio streaming started")
    
    def _handle_streaming_finished(self):
        logger.info("🏁 Audio streaming finished")
    
    def _handle_streamer_error(self, error: Exception):
        logger.error(f"Audio streamer error: {error}")
    
    def get_live_stats(self) -> Dict[str, Any]:
        """Get live meeting statistics"""
        current_time = time.time()
        
        # Calculate speaking time percentages
        total_speaking_time = sum(
            stats['total_time'] for stats in self.meeting_stats.speakers.values()
        )
        
        speaker_percentages = {}
        for speaker_id, stats in self.meeting_stats.speakers.items():
            percentage = (stats['total_time'] / total_speaking_time * 100) if total_speaking_time > 0 else 0
            speaker_percentages[speaker_id] = {
                **stats,
                'percentage': percentage
            }
        
        return {
            'meeting_stats': self.meeting_stats,
            'speaker_percentages': speaker_percentages,
            'recent_segments': list(self.recent_segments),
            'deepgram_stats': self.deepgram_service.get_stats(),
            'streamer_stats': self.audio_streamer.get_stats(),
            'current_time': current_time
        }


# Example usage
if __name__ == "__main__":
    import asyncio
    
    async def main():
        # Get API key
        api_key = os.getenv('DEEPGRAM_API_KEY')
        if not api_key:
            print("❌ DEEPGRAM_API_KEY environment variable required")
            return
        
        # Initialize processor
        processor = RealTimeMeetingProcessor(
            deepgram_api_key=api_key,
            audio_file_path="test_audio/meeting_example.wav",  # Default example audio
            chunk_duration_ms=100,
            speed_multiplier=2.0  # 2x speed for demo
        )
        
        # Setup callbacks
        def on_transcript_update(segment):
            print(f"📝 {segment.speaker_id}: {segment.text}")
        
        def on_meeting_insight(insight):
            print(f"💡 {insight['type'].upper()}: {insight['message']}")
        
        processor.on_transcript_update = on_transcript_update
        processor.on_meeting_insight = on_meeting_insight
        
        # Start processing
        if await processor.start_processing():
            print("✅ Processing started")
            
            # Let it run for a while
            await asyncio.sleep(30)
            
            # Stop processing
            await processor.stop_processing()
            
            # Print final stats
            stats = processor.get_live_stats()
            print(f"📊 Final stats: {stats}")
        else:
            print("❌ Failed to start processing")
    
    asyncio.run(main())
