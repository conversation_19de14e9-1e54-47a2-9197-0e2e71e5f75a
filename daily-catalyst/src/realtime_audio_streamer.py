#!/usr/bin/env python3
"""
Real-Time Audio Streamer for Daily Catalyst

This module simulates real-time audio streaming from audio files,
allowing testing of real-time transcription systems.
"""

import asyncio
import logging
import time
from typing import Optional, Callable, Generator
from pathlib import Path
import io

from pydub import AudioSegment
from pydub.exceptions import CouldntDecodeError

# Configure logging
logger = logging.getLogger(__name__)


class AudioStreamerError(Exception):
    """Custom exception for audio streamer errors"""
    pass


class RealTimeAudioStreamer:
    """
    Streams audio file in real-time chunks to simulate live audio input.
    
    Features:
    - Configurable chunk size and timing
    - Real-time playback speed simulation
    - Support for various audio formats
    - Automatic format conversion to target specs
    - Pause/resume functionality
    """
    
    def __init__(self,
                 chunk_duration_ms: int = 100,  # 100ms chunks
                 target_sample_rate: int = 16000,
                 target_channels: int = 1,
                 target_sample_width: int = 2,  # 16-bit
                 speed_multiplier: float = 1.0):
        """
        Initialize audio streamer.
        
        Args:
            chunk_duration_ms: Duration of each audio chunk in milliseconds
            target_sample_rate: Target sample rate (16kHz recommended for STT)
            target_channels: Target number of channels (1 for mono)
            target_sample_width: Target sample width in bytes (2 for 16-bit)
            speed_multiplier: Playback speed multiplier (1.0 = normal speed)
        """
        self.chunk_duration_ms = chunk_duration_ms
        self.target_sample_rate = target_sample_rate
        self.target_channels = target_channels
        self.target_sample_width = target_sample_width
        self.speed_multiplier = speed_multiplier
        
        # Audio data
        self.audio: Optional[AudioSegment] = None
        self.audio_file_path: Optional[str] = None
        
        # Streaming state
        self.is_streaming = False
        self.is_paused = False
        self.current_position_ms = 0
        self.total_duration_ms = 0
        
        # Callbacks
        self.on_audio_chunk: Optional[Callable[[bytes, float, float], None]] = None
        self.on_streaming_started: Optional[Callable[[], None]] = None
        self.on_streaming_finished: Optional[Callable[[], None]] = None
        self.on_error: Optional[Callable[[Exception], None]] = None
        
        # Statistics
        self.stats = {
            'chunks_sent': 0,
            'bytes_sent': 0,
            'streaming_start_time': 0,
            'total_streaming_time': 0
        }
        
        logger.info(f"RealTimeAudioStreamer initialized: chunk_duration={chunk_duration_ms}ms, "
                   f"sample_rate={target_sample_rate}Hz, speed={speed_multiplier}x")
    
    def load_audio_file(self, file_path: str) -> bool:
        """
        Load audio file for streaming.
        
        Args:
            file_path: Path to audio file
            
        Returns:
            True if loaded successfully, False otherwise
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                raise AudioStreamerError(f"Audio file not found: {file_path}")
            
            logger.info(f"Loading audio file: {file_path}")
            
            # Load audio
            self.audio = AudioSegment.from_file(str(file_path))
            self.audio_file_path = str(file_path)
            
            # Convert to target format
            self.audio = self._convert_to_target_format(self.audio)
            
            self.total_duration_ms = len(self.audio)
            self.current_position_ms = 0
            
            logger.info(f"Audio loaded: duration={self.total_duration_ms}ms, "
                       f"sample_rate={self.audio.frame_rate}Hz, "
                       f"channels={self.audio.channels}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to load audio file: {e}")
            if self.on_error:
                self.on_error(AudioStreamerError(f"Load error: {e}"))
            return False
    
    def _convert_to_target_format(self, audio: AudioSegment) -> AudioSegment:
        """Convert audio to target format"""
        logger.debug("Converting audio to target format")
        
        # Convert channels
        if audio.channels != self.target_channels:
            logger.debug(f"Converting from {audio.channels} to {self.target_channels} channels")
            audio = audio.set_channels(self.target_channels)
        
        # Convert sample rate
        if audio.frame_rate != self.target_sample_rate:
            logger.debug(f"Converting sample rate from {audio.frame_rate}Hz to {self.target_sample_rate}Hz")
            audio = audio.set_frame_rate(self.target_sample_rate)
        
        # Convert sample width
        if audio.sample_width != self.target_sample_width:
            logger.debug(f"Converting sample width from {audio.sample_width} to {self.target_sample_width} bytes")
            audio = audio.set_sample_width(self.target_sample_width)
        
        return audio
    
    async def start_streaming(self) -> bool:
        """
        Start streaming audio in real-time.
        
        Returns:
            True if streaming started successfully, False otherwise
        """
        if not self.audio:
            logger.error("No audio loaded")
            return False
        
        if self.is_streaming:
            logger.warning("Already streaming")
            return True
        
        try:
            self.is_streaming = True
            self.is_paused = False
            self.stats['streaming_start_time'] = time.time()
            self.stats['chunks_sent'] = 0
            self.stats['bytes_sent'] = 0
            
            logger.info(f"Starting audio streaming: {self.total_duration_ms}ms at {self.speed_multiplier}x speed")
            
            if self.on_streaming_started:
                self.on_streaming_started()
            
            # Start streaming task
            await self._stream_audio_chunks()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to start streaming: {e}")
            self.is_streaming = False
            if self.on_error:
                self.on_error(AudioStreamerError(f"Streaming error: {e}"))
            return False
    
    async def stop_streaming(self):
        """Stop audio streaming"""
        self.is_streaming = False
        self.is_paused = False
        
        if self.stats['streaming_start_time']:
            self.stats['total_streaming_time'] += time.time() - self.stats['streaming_start_time']
        
        logger.info("Audio streaming stopped")
        
        if self.on_streaming_finished:
            self.on_streaming_finished()
    
    def pause_streaming(self):
        """Pause audio streaming"""
        self.is_paused = True
        logger.info("Audio streaming paused")
    
    def resume_streaming(self):
        """Resume audio streaming"""
        self.is_paused = False
        logger.info("Audio streaming resumed")
    
    def seek_to_position(self, position_ms: int):
        """
        Seek to specific position in audio.
        
        Args:
            position_ms: Position in milliseconds
        """
        if position_ms < 0:
            position_ms = 0
        elif position_ms > self.total_duration_ms:
            position_ms = self.total_duration_ms
        
        self.current_position_ms = position_ms
        logger.info(f"Seeked to position: {position_ms}ms")
    
    async def _stream_audio_chunks(self):
        """Stream audio chunks in real-time"""
        try:
            while self.is_streaming and self.current_position_ms < self.total_duration_ms:
                if self.is_paused:
                    await asyncio.sleep(0.1)
                    continue
                
                # Calculate chunk timing
                chunk_start_ms = self.current_position_ms
                chunk_end_ms = min(chunk_start_ms + self.chunk_duration_ms, self.total_duration_ms)
                
                # Extract audio chunk
                audio_chunk = self.audio[chunk_start_ms:chunk_end_ms]
                
                # Convert to raw bytes
                chunk_bytes = audio_chunk.raw_data
                
                # Calculate timing
                chunk_start_seconds = chunk_start_ms / 1000.0
                chunk_end_seconds = chunk_end_ms / 1000.0
                
                # Send chunk via callback
                if self.on_audio_chunk:
                    if asyncio.iscoroutinefunction(self.on_audio_chunk):
                        await self.on_audio_chunk(chunk_bytes, chunk_start_seconds, chunk_end_seconds)
                    else:
                        self.on_audio_chunk(chunk_bytes, chunk_start_seconds, chunk_end_seconds)
                
                # Update statistics
                self.stats['chunks_sent'] += 1
                self.stats['bytes_sent'] += len(chunk_bytes)
                
                # Update position
                self.current_position_ms = chunk_end_ms
                
                # Calculate sleep time for real-time playback
                actual_chunk_duration = self.chunk_duration_ms / 1000.0
                sleep_duration = actual_chunk_duration / self.speed_multiplier
                
                # Sleep to maintain real-time timing
                await asyncio.sleep(sleep_duration)
            
            # Streaming finished
            if self.is_streaming:
                await self.stop_streaming()
                
        except Exception as e:
            logger.error(f"Error during streaming: {e}")
            if self.on_error:
                self.on_error(AudioStreamerError(f"Streaming error: {e}"))
    
    def get_progress(self) -> dict:
        """Get streaming progress information"""
        if not self.audio:
            return {'progress_percent': 0, 'current_time': 0, 'total_time': 0}
        
        progress_percent = (self.current_position_ms / self.total_duration_ms) * 100 if self.total_duration_ms > 0 else 0
        
        return {
            'progress_percent': progress_percent,
            'current_time_ms': self.current_position_ms,
            'total_time_ms': self.total_duration_ms,
            'current_time_seconds': self.current_position_ms / 1000.0,
            'total_time_seconds': self.total_duration_ms / 1000.0,
            'is_streaming': self.is_streaming,
            'is_paused': self.is_paused
        }
    
    def get_stats(self) -> dict:
        """Get streaming statistics"""
        current_time = time.time()
        
        if self.is_streaming and self.stats['streaming_start_time']:
            current_streaming_time = current_time - self.stats['streaming_start_time']
        else:
            current_streaming_time = 0
        
        total_time = self.stats['total_streaming_time'] + current_streaming_time
        
        return {
            **self.stats,
            'current_streaming_time': current_streaming_time,
            'total_streaming_time': total_time,
            'chunks_per_second': self.stats['chunks_sent'] / total_time if total_time > 0 else 0,
            'bytes_per_second': self.stats['bytes_sent'] / total_time if total_time > 0 else 0,
            **self.get_progress()
        }


# Example usage
if __name__ == "__main__":
    import asyncio
    
    async def on_audio_chunk(chunk_bytes: bytes, start_time: float, end_time: float):
        """Handle audio chunk"""
        print(f"📡 Audio chunk: {start_time:.2f}s-{end_time:.2f}s, {len(chunk_bytes)} bytes")
    
    def on_streaming_started():
        print("🎵 Streaming started")
    
    def on_streaming_finished():
        print("🏁 Streaming finished")
    
    async def main():
        # Initialize streamer
        streamer = RealTimeAudioStreamer(
            chunk_duration_ms=100,  # 100ms chunks
            speed_multiplier=1.0    # Normal speed
        )
        
        streamer.on_audio_chunk = on_audio_chunk
        streamer.on_streaming_started = on_streaming_started
        streamer.on_streaming_finished = on_streaming_finished
        
        # Load audio file
        audio_file = "test_audio/meeting_example.wav"  # Default example audio
        
        if streamer.load_audio_file(audio_file):
            print(f"✅ Audio loaded: {streamer.total_duration_ms}ms")
            
            # Start streaming
            await streamer.start_streaming()
            
            # Print final stats
            stats = streamer.get_stats()
            print(f"📊 Final stats: {stats['chunks_sent']} chunks, {stats['bytes_sent']} bytes")
        else:
            print("❌ Failed to load audio file")
    
    asyncio.run(main())
