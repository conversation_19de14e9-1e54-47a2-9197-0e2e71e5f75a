#!/usr/bin/env python3
"""
Deepgram Real-Time Speech-to-Text Service for Daily Catalyst

This module provides real-time speech recognition and speaker diarization
using Deepgram's WebSocket API.
"""

import asyncio
import json
import logging
import os
import time
from typing import Optional, Callable, Dict, Any, List
from dataclasses import dataclass
from enum import Enum

import websockets
from websockets.exceptions import ConnectionClosed, WebSocketException

# Configure logging
logger = logging.getLogger(__name__)


class DeepgramSTTError(Exception):
    """Custom exception for Deepgram STT errors"""
    pass


class TranscriptionStatus(Enum):
    """Status of transcription result"""
    INTERIM = "interim"
    FINAL = "final"
    SPEECH_FINAL = "speech_final"


@dataclass
class DeepgramWord:
    """Word information from Deepgram"""
    word: str
    start: float
    end: float
    confidence: float
    speaker: Optional[int] = None
    punctuated_word: Optional[str] = None


@dataclass
class DeepgramTranscriptionResult:
    """Transcription result from Deepgram"""
    transcript: str
    confidence: float
    words: List[DeepgramWord]
    is_final: bool
    speech_final: bool
    duration: float
    start: float
    channel_index: List[int]
    request_id: str
    model_info: Dict[str, Any]
    timestamp: float


class DeepgramSTTService:
    """
    Real-time Speech-to-Text service using Deepgram WebSocket API.
    
    Features:
    - Real-time transcription with low latency
    - Speaker diarization
    - Smart formatting
    - Interim and final results
    - Automatic reconnection
    """
    
    def __init__(self,
                 api_key: str,
                 model: str = "nova-3",
                 language: str = "en-US",
                 enable_diarization: bool = True,
                 enable_smart_format: bool = True,
                 enable_interim_results: bool = True,
                 sample_rate: int = 16000,
                 channels: int = 1,
                 encoding: str = "linear16"):
        """
        Initialize Deepgram STT service.
        
        Args:
            api_key: Deepgram API key
            model: Model to use (nova-3, nova-2, enhanced, base)
            language: Language code (en-US, en-GB, etc.)
            enable_diarization: Enable speaker diarization
            enable_smart_format: Enable smart formatting
            enable_interim_results: Enable interim results
            sample_rate: Audio sample rate (16000 recommended)
            channels: Number of audio channels (1 for mono)
            encoding: Audio encoding (linear16, mulaw, etc.)
        """
        self.api_key = api_key
        self.model = model
        self.language = language
        self.enable_diarization = enable_diarization
        self.enable_smart_format = enable_smart_format
        self.enable_interim_results = enable_interim_results
        self.sample_rate = sample_rate
        self.channels = channels
        self.encoding = encoding
        
        # WebSocket connection
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        self.is_connected = False
        self.is_streaming = False
        
        # Callbacks
        self.on_transcript: Optional[Callable[[DeepgramTranscriptionResult], None]] = None
        self.on_error: Optional[Callable[[Exception], None]] = None
        self.on_connection_opened: Optional[Callable[[], None]] = None
        self.on_connection_closed: Optional[Callable[[], None]] = None
        
        # Statistics
        self.stats = {
            'total_transcripts': 0,
            'interim_results': 0,
            'final_results': 0,
            'speech_final_results': 0,
            'connection_time': 0,
            'last_activity': 0
        }
        
        logger.info(f"DeepgramSTTService initialized: model={model}, language={language}, "
                   f"diarization={enable_diarization}")
    
    def _build_websocket_url(self) -> str:
        """Build WebSocket URL with parameters"""
        base_url = "wss://api.deepgram.com/v1/listen"
        
        params = {
            'model': self.model,
            'language': self.language,
            'sample_rate': self.sample_rate,
            'channels': self.channels,
            'encoding': self.encoding,
            'punctuate': 'true',
            'smart_format': str(self.enable_smart_format).lower(),
            'interim_results': str(self.enable_interim_results).lower(),
            'diarize': str(self.enable_diarization).lower(),
            'filler_words': 'true',
            'utterances': 'true'
        }
        
        # Build query string
        query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
        url = f"{base_url}?{query_string}"
        
        logger.debug(f"WebSocket URL: {url}")
        return url
    
    async def connect(self) -> bool:
        """
        Connect to Deepgram WebSocket API.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            url = self._build_websocket_url()

            logger.info("Connecting to Deepgram WebSocket...")
            # Use Sec-WebSocket-Protocol for authentication as per Deepgram docs
            subprotocols = ['token', self.api_key]
            self.websocket = await websockets.connect(url, subprotocols=subprotocols)
            self.is_connected = True
            self.stats['connection_time'] = time.time()
            
            logger.info("✅ Connected to Deepgram WebSocket")
            
            if self.on_connection_opened:
                self.on_connection_opened()
            
            # Start listening for messages
            asyncio.create_task(self._listen_for_messages())
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to Deepgram: {e}")
            self.is_connected = False
            if self.on_error:
                self.on_error(DeepgramSTTError(f"Connection failed: {e}"))
            return False
    
    async def disconnect(self):
        """Disconnect from Deepgram WebSocket"""
        try:
            self.is_streaming = False
            self.is_connected = False
            
            if self.websocket:
                await self.websocket.close()
                self.websocket = None
                
            logger.info("Disconnected from Deepgram WebSocket")
            
            if self.on_connection_closed:
                self.on_connection_closed()
                
        except Exception as e:
            logger.error(f"Error during disconnect: {e}")
    
    async def send_audio(self, audio_data: bytes):
        """
        Send audio data to Deepgram.
        
        Args:
            audio_data: Raw audio bytes (16kHz, mono, 16-bit)
        """
        if not self.is_connected or not self.websocket:
            raise DeepgramSTTError("Not connected to Deepgram")
        
        try:
            await self.websocket.send(audio_data)
            self.stats['last_activity'] = time.time()
            
        except ConnectionClosed:
            logger.warning("WebSocket connection closed")
            self.is_connected = False
            if self.on_error:
                self.on_error(DeepgramSTTError("Connection closed"))
                
        except Exception as e:
            logger.error(f"Error sending audio: {e}")
            if self.on_error:
                self.on_error(DeepgramSTTError(f"Send error: {e}"))
    
    async def _listen_for_messages(self):
        """Listen for messages from Deepgram WebSocket"""
        try:
            async for message in self.websocket:
                await self._process_message(message)
                
        except ConnectionClosed:
            logger.warning("WebSocket connection closed while listening")
            self.is_connected = False
            
        except Exception as e:
            logger.error(f"Error listening for messages: {e}")
            if self.on_error:
                self.on_error(DeepgramSTTError(f"Listen error: {e}"))
    
    async def _process_message(self, message: str):
        """Process incoming message from Deepgram"""
        try:
            data = json.loads(message)
            
            if data.get('type') == 'Results':
                result = self._parse_transcription_result(data)
                
                # Update statistics
                self.stats['total_transcripts'] += 1
                if result.is_final:
                    self.stats['final_results'] += 1
                else:
                    self.stats['interim_results'] += 1
                    
                if result.speech_final:
                    self.stats['speech_final_results'] += 1
                
                # Call transcript callback
                if self.on_transcript:
                    self.on_transcript(result)
                    
            elif data.get('type') == 'Metadata':
                logger.debug(f"Metadata received: {data}")
                
            else:
                logger.debug(f"Unknown message type: {data.get('type')}")
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON message: {e}")
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
    
    def _parse_transcription_result(self, data: Dict[str, Any]) -> DeepgramTranscriptionResult:
        """Parse transcription result from Deepgram response"""
        channel = data.get('channel', {})
        alternatives = channel.get('alternatives', [{}])
        alternative = alternatives[0] if alternatives else {}
        
        # Parse words
        words = []
        for word_data in alternative.get('words', []):
            word = DeepgramWord(
                word=word_data.get('word', ''),
                start=word_data.get('start', 0.0),
                end=word_data.get('end', 0.0),
                confidence=word_data.get('confidence', 0.0),
                speaker=word_data.get('speaker'),
                punctuated_word=word_data.get('punctuated_word')
            )
            words.append(word)
        
        # Create result
        result = DeepgramTranscriptionResult(
            transcript=alternative.get('transcript', ''),
            confidence=alternative.get('confidence', 0.0),
            words=words,
            is_final=data.get('is_final', False),
            speech_final=data.get('speech_final', False),
            duration=data.get('duration', 0.0),
            start=data.get('start', 0.0),
            channel_index=data.get('channel_index', []),
            request_id=data.get('metadata', {}).get('request_id', ''),
            model_info=data.get('metadata', {}).get('model_info', {}),
            timestamp=time.time()
        )
        
        return result
    
    def get_stats(self) -> Dict[str, Any]:
        """Get service statistics"""
        current_time = time.time()
        connection_duration = current_time - self.stats['connection_time'] if self.stats['connection_time'] else 0
        
        return {
            **self.stats,
            'connection_duration': connection_duration,
            'is_connected': self.is_connected,
            'is_streaming': self.is_streaming
        }


# Example usage
if __name__ == "__main__":
    import asyncio
    
    async def on_transcript(result: DeepgramTranscriptionResult):
        """Handle transcription result"""
        status = "🔴 INTERIM" if not result.is_final else "🟢 FINAL"
        if result.speech_final:
            status = "🔵 SPEECH_FINAL"
            
        print(f"{status} [{result.start:.1f}s]: {result.transcript}")
        
        if result.words and result.enable_diarization:
            speakers = set(w.speaker for w in result.words if w.speaker is not None)
            if speakers:
                print(f"  👥 Speakers: {sorted(speakers)}")
    
    async def main():
        # Initialize service
        api_key = os.getenv('DEEPGRAM_API_KEY')
        if not api_key:
            print("❌ DEEPGRAM_API_KEY environment variable required")
            return
            
        service = DeepgramSTTService(
            api_key=api_key,
            model="nova-3",
            language="en-US",
            enable_diarization=True
        )
        
        service.on_transcript = on_transcript
        
        # Connect
        if await service.connect():
            print("✅ Connected to Deepgram")
            
            # Keep connection alive for demo
            await asyncio.sleep(10)
            
            await service.disconnect()
        else:
            print("❌ Failed to connect")
    
    asyncio.run(main())
