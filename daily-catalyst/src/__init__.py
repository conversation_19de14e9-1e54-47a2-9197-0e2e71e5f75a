"""
DailyCat - Daily Standup Moderator Bot

Audio processing and speech recognition modules for automated meeting moderation.
"""

__version__ = "0.1.0"
__author__ = "DailyCat Team"

from .audio_processor import AudioProcessor, load_audio, AudioProcessorError
from .stt_service import (
    STTService,
    STTServiceError,
    TranscriptionResult,
    WordInfo,
    SpeakerSegment,
    TranscriptionStatus,
    create_stt_client
)
from .free_stt_service import (
    FreeSSTService,
    create_free_stt_client
)

__all__ = [
    # Audio processing
    "AudioProcessor",
    "load_audio",
    "AudioProcessorError",

    # Speech-to-Text (Google Cloud)
    "STTService",
    "STTServiceError",
    "TranscriptionResult",
    "WordInfo",
    "SpeakerSegment",
    "TranscriptionStatus",
    "create_stt_client",

    # Free Speech-to-Text
    "FreeSSTService",
    "create_free_stt_client"
]
