#!/usr/bin/env python3
"""
Audio Processing Module for DailyCat

This module handles loading audio files and splitting them into chunks
for streaming speech recognition processing.
"""

import logging
import os
from typing import Generator, List, Optional, Tuple
from pathlib import Path
import io

from pydub import AudioSegment
from pydub.exceptions import CouldntDecodeError


# Configure logging
logger = logging.getLogger(__name__)


class AudioProcessorError(Exception):
    """Custom exception for audio processing errors"""
    pass


class AudioProcessor:
    """
    Audio processor for loading and chunking audio files.
    
    Handles conversion to the format required by Google Speech-to-Text:
    - Sample rate: 16kHz
    - Channels: mono
    - Format: WAV PCM 16-bit
    """
    
    # Target format for Google STT
    TARGET_SAMPLE_RATE = 16000  # 16kHz
    TARGET_CHANNELS = 1  # mono
    TARGET_SAMPLE_WIDTH = 2  # 16-bit (2 bytes)
    
    # Chunk settings
    DEFAULT_CHUNK_SIZE_MS = 1000  # 1 second chunks
    DEFAULT_OVERLAP_MS = 100  # 100ms overlap between chunks
    
    # Supported audio formats
    SUPPORTED_FORMATS = {'.wav', '.mp3', '.m4a', '.flac', '.ogg', '.aac', '.wma'}
    
    def __init__(self, 
                 chunk_size_ms: int = DEFAULT_CHUNK_SIZE_MS,
                 overlap_ms: int = DEFAULT_OVERLAP_MS):
        """
        Initialize AudioProcessor.
        
        Args:
            chunk_size_ms: Size of each audio chunk in milliseconds (500-1000ms recommended)
            overlap_ms: Overlap between chunks in milliseconds for better quality
        """
        if chunk_size_ms < 100 or chunk_size_ms > 5000:
            raise ValueError("chunk_size_ms must be between 100 and 5000 milliseconds")
        
        if overlap_ms < 0 or overlap_ms >= chunk_size_ms:
            raise ValueError("overlap_ms must be between 0 and chunk_size_ms")
        
        self.chunk_size_ms = chunk_size_ms
        self.overlap_ms = overlap_ms
        
        logger.info(f"AudioProcessor initialized: chunk_size={chunk_size_ms}ms, overlap={overlap_ms}ms")
    
    def load_audio(self, file_path: str) -> AudioSegment:
        """
        Load audio file and return AudioSegment.
        
        Args:
            file_path: Path to the audio file
            
        Returns:
            AudioSegment object
            
        Raises:
            AudioProcessorError: If file cannot be loaded or format is unsupported
        """
        file_path = Path(file_path)
        
        # Check if file exists
        if not file_path.exists():
            raise AudioProcessorError(f"Audio file not found: {file_path}")
        
        # Check file extension
        if file_path.suffix.lower() not in self.SUPPORTED_FORMATS:
            raise AudioProcessorError(
                f"Unsupported audio format: {file_path.suffix}. "
                f"Supported formats: {', '.join(self.SUPPORTED_FORMATS)}"
            )
        
        # Check file size (limit to 100MB for safety)
        file_size_mb = file_path.stat().st_size / (1024 * 1024)
        if file_size_mb > 100:
            logger.warning(f"Large audio file detected: {file_size_mb:.1f}MB")
        
        try:
            logger.info(f"Loading audio file: {file_path}")
            audio = AudioSegment.from_file(str(file_path))
            
            logger.info(f"Audio loaded successfully: "
                       f"duration={len(audio)}ms, "
                       f"channels={audio.channels}, "
                       f"sample_rate={audio.frame_rate}Hz, "
                       f"sample_width={audio.sample_width} bytes")
            
            return audio
            
        except CouldntDecodeError as e:
            raise AudioProcessorError(f"Could not decode audio file {file_path}: {e}")
        except Exception as e:
            raise AudioProcessorError(f"Error loading audio file {file_path}: {e}")
    
    def convert_to_target_format(self, audio: AudioSegment) -> AudioSegment:
        """
        Convert audio to target format for Google STT.
        
        Args:
            audio: Input AudioSegment
            
        Returns:
            AudioSegment in target format (16kHz, mono, 16-bit)
        """
        logger.debug("Converting audio to target format")
        
        # Convert to mono if stereo
        if audio.channels > 1:
            logger.debug(f"Converting from {audio.channels} channels to mono")
            audio = audio.set_channels(self.TARGET_CHANNELS)
        
        # Convert sample rate to 16kHz
        if audio.frame_rate != self.TARGET_SAMPLE_RATE:
            logger.debug(f"Converting sample rate from {audio.frame_rate}Hz to {self.TARGET_SAMPLE_RATE}Hz")
            audio = audio.set_frame_rate(self.TARGET_SAMPLE_RATE)
        
        # Convert to 16-bit
        if audio.sample_width != self.TARGET_SAMPLE_WIDTH:
            logger.debug(f"Converting sample width from {audio.sample_width} to {self.TARGET_SAMPLE_WIDTH} bytes")
            audio = audio.set_sample_width(self.TARGET_SAMPLE_WIDTH)
        
        logger.info("Audio converted to target format: 16kHz, mono, 16-bit")
        return audio
    
    def create_chunks(self, audio: AudioSegment) -> Generator[Tuple[AudioSegment, int, int], None, None]:
        """
        Split audio into overlapping chunks.
        
        Args:
            audio: AudioSegment to split
            
        Yields:
            Tuple of (chunk_audio, start_time_ms, end_time_ms)
        """
        total_duration = len(audio)
        logger.info(f"Creating chunks from audio: duration={total_duration}ms, "
                   f"chunk_size={self.chunk_size_ms}ms, overlap={self.overlap_ms}ms")
        
        if total_duration <= self.chunk_size_ms:
            # Audio is shorter than chunk size, return as single chunk
            logger.info("Audio shorter than chunk size, returning single chunk")
            yield audio, 0, total_duration
            return
        
        step_size = self.chunk_size_ms - self.overlap_ms
        chunk_count = 0
        
        for start_ms in range(0, total_duration, step_size):
            end_ms = min(start_ms + self.chunk_size_ms, total_duration)
            
            # Skip very small chunks at the end
            if end_ms - start_ms < self.chunk_size_ms // 2:
                logger.debug(f"Skipping small chunk at end: {end_ms - start_ms}ms")
                break
            
            chunk = audio[start_ms:end_ms]
            chunk_count += 1
            
            logger.debug(f"Created chunk {chunk_count}: {start_ms}-{end_ms}ms ({len(chunk)}ms)")
            yield chunk, start_ms, end_ms
            
            # Break if we've reached the end
            if end_ms >= total_duration:
                break
        
        logger.info(f"Created {chunk_count} chunks total")
    
    def chunk_to_wav_bytes(self, chunk: AudioSegment) -> bytes:
        """
        Convert audio chunk to WAV bytes for API transmission.
        
        Args:
            chunk: AudioSegment chunk
            
        Returns:
            WAV audio data as bytes
        """
        buffer = io.BytesIO()
        chunk.export(buffer, format="wav")
        return buffer.getvalue()
    
    def process_audio_file(self, file_path: str) -> Generator[Tuple[bytes, int, int], None, None]:
        """
        Complete audio processing pipeline: load, convert, and chunk audio file.
        
        Args:
            file_path: Path to audio file
            
        Yields:
            Tuple of (wav_bytes, start_time_ms, end_time_ms)
            
        Raises:
            AudioProcessorError: If processing fails
        """
        try:
            # Load audio file
            audio = self.load_audio(file_path)
            
            # Convert to target format
            audio = self.convert_to_target_format(audio)
            
            # Create and yield chunks
            for chunk, start_ms, end_ms in self.create_chunks(audio):
                wav_bytes = self.chunk_to_wav_bytes(chunk)
                yield wav_bytes, start_ms, end_ms
                
        except Exception as e:
            logger.error(f"Error processing audio file {file_path}: {e}")
            raise AudioProcessorError(f"Failed to process audio file: {e}")


def load_audio(file_path: str) -> AudioSegment:
    """
    Simple function to load audio file (as specified in requirements).
    
    Args:
        file_path: Path to audio file
        
    Returns:
        AudioSegment object
    """
    processor = AudioProcessor()
    return processor.load_audio(file_path)


# Example usage
if __name__ == "__main__":
    # Configure logging for demo
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # Example usage
    processor = AudioProcessor(chunk_size_ms=1000, overlap_ms=100)
    
    # This would process an audio file if it existed
    # for wav_bytes, start_ms, end_ms in processor.process_audio_file("example.wav"):
    #     print(f"Chunk: {start_ms}-{end_ms}ms, size: {len(wav_bytes)} bytes")
