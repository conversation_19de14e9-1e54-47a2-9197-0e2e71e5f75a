# Core dependencies for Daily Catalyst
python-dotenv>=0.19.0

# Audio processing
pydub>=0.25.0

# Deepgram integration
deepgram-sdk>=3.0.0
websockets>=10.0
aiohttp>=3.8.0

# Google Gemini API for off-topic detection
google-generativeai>=0.3.0

# Utilities
tqdm>=4.62.0

# Development (optional)
pytest>=6.2.0
black>=22.0.0
flake8>=4.0.0

# Optional: Advanced ML features (uncomment if needed)
# numpy>=1.21.0
# scipy>=1.7.0
# librosa>=0.9.0
# soundfile>=0.10.0
# torch>=1.9.0
# torchaudio>=0.9.0
# speechbrain>=0.5.0
# pyannote.audio>=2.1.0
# scikit-learn>=1.0.0
# transformers>=4.20.0
# pandas>=1.3.0
# matplotlib>=3.5.0

# Optional: Alternative LLM providers
# openai>=0.27.0
# anthropic>=0.3.0
