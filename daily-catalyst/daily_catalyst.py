#!/usr/bin/env python3
"""
Daily Catalyst - AI-powered Daily Standup Meeting Analyzer
Main entry point for the application
"""

import sys
import os
import argparse
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def setup_logging(verbose=False):
    """Setup logging configuration"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('daily_catalyst.log')
        ]
    )

def create_embeddings(args):
    """Create speaker embeddings from audio file"""
    print("🧬 Creating Speaker Embeddings...")
    
    try:
        from embeddings.embeddings_creator import ImprovedEmbeddingsCreator
        
        creator = ImprovedEmbeddingsCreator(args.audio_file)
        success = creator.create_improved_embeddings()
        
        if success:
            print("✅ Speaker embeddings created successfully!")
            return True
        else:
            print("❌ Failed to create speaker embeddings")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all dependencies are installed: pip install -r requirements.txt")
        return False
    except Exception as e:
        print(f"❌ Error creating embeddings: {e}")
        return False

def create_transcript(args):
    """Create complete transcript with speaker identification"""
    print("📝 Creating Complete Transcript...")
    
    try:
        from transcription.transcript_creator import CompleteTranscriptCreator
        
        creator = CompleteTranscriptCreator(
            args.audio_file, 
            chunk_duration=args.chunk_duration
        )
        success = creator.create_complete_transcript()
        
        if success:
            print("✅ Complete transcript created successfully!")
            analysis_file = creator.get_analysis_file()
            if analysis_file:
                print(f"📄 Analysis file: {analysis_file}")
            return True
        else:
            print("❌ Failed to create transcript")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error creating transcript: {e}")
        return False

def analyze_meeting(args):
    """Analyze meeting for off-topic discussions"""
    print("🤖 Analyzing Meeting...")
    
    try:
        from analysis.meeting_analyzer import DailyMeetingAnalyzer
        
        analyzer = DailyMeetingAnalyzer(args.transcript_file)
        success = analyzer.analyze_meeting()
        
        if success:
            analyzer.display_summary()
            print("✅ Meeting analysis completed successfully!")
            return True
        else:
            print("❌ Failed to analyze meeting")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error analyzing meeting: {e}")
        return False

def run_realtime(args):
    """Run real-time processing"""
    print("🚀 Starting Real-time Processing...")
    
    try:
        from core.realtime_engine import simulate_realtime_transcription
        
        success = simulate_realtime_transcription(
            audio_file=args.audio_file,
            speaker_method=args.speaker_method
        )
        
        if success:
            print("✅ Real-time processing completed successfully!")
            return True
        else:
            print("❌ Real-time processing failed")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error in real-time processing: {e}")
        return False

def full_pipeline(args):
    """Run the complete analysis pipeline"""
    print("🔄 Running Complete Analysis Pipeline...")
    print("=" * 60)
    
    # Step 1: Create embeddings if they don't exist
    embeddings_exist = (
        os.path.exists("improved_speaker_embeddings.pkl") or 
        os.path.exists("speaker_embeddings.pkl")
    )
    
    if not embeddings_exist:
        print("Step 1: Creating speaker embeddings...")
        if not create_embeddings(args):
            return False
    else:
        print("Step 1: ✅ Speaker embeddings already exist")
    
    # Step 2: Create transcript
    print("\nStep 2: Creating complete transcript...")
    if not create_transcript(args):
        return False
    
    # Step 3: Analyze meeting
    print("\nStep 3: Analyzing meeting...")
    if not analyze_meeting(args):
        return False
    
    print("\n🎉 COMPLETE PIPELINE FINISHED!")
    print("✅ Speaker embeddings ready")
    print("✅ Complete transcript created")
    print("✅ Meeting analyzed")
    print("✅ Reports generated")
    
    return True

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Daily Catalyst - AI-powered Daily Standup Meeting Analyzer",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run complete pipeline
  python daily_catalyst.py pipeline --audio-file meeting.wav
  
  # Create speaker embeddings only
  python daily_catalyst.py embeddings --audio-file meeting.wav
  
  # Create transcript only
  python daily_catalyst.py transcript --audio-file meeting.wav
  
  # Analyze existing transcript
  python daily_catalyst.py analyze --transcript-file transcript.txt
  
  # Real-time processing
  python daily_catalyst.py realtime --audio-file meeting.wav
        """
    )
    
    parser.add_argument('--verbose', '-v', action='store_true', 
                       help='Enable verbose logging')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Pipeline command
    pipeline_parser = subparsers.add_parser('pipeline', help='Run complete analysis pipeline')
    pipeline_parser.add_argument('--audio-file', required=True, 
                                help='Path to audio file')
    pipeline_parser.add_argument('--chunk-duration', type=float, default=4.0,
                                help='Audio chunk duration in seconds (default: 4.0)')
    
    # Embeddings command
    embeddings_parser = subparsers.add_parser('embeddings', help='Create speaker embeddings')
    embeddings_parser.add_argument('--audio-file', required=True,
                                  help='Path to audio file')
    
    # Transcript command
    transcript_parser = subparsers.add_parser('transcript', help='Create complete transcript')
    transcript_parser.add_argument('--audio-file', required=True,
                                  help='Path to audio file')
    transcript_parser.add_argument('--chunk-duration', type=float, default=4.0,
                                  help='Audio chunk duration in seconds (default: 4.0)')
    
    # Analyze command
    analyze_parser = subparsers.add_parser('analyze', help='Analyze meeting transcript')
    analyze_parser.add_argument('--transcript-file', 
                               help='Path to transcript file (optional, will find latest)')
    
    # Realtime command
    realtime_parser = subparsers.add_parser('realtime', help='Real-time processing')
    realtime_parser.add_argument('--audio-file', required=True,
                                help='Path to audio file')
    realtime_parser.add_argument('--speaker-method', default='embeddings',
                                choices=['embeddings', 'callhome', 'pyannote', 'simple'],
                                help='Speaker identification method (default: embeddings)')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    # Setup logging
    setup_logging(args.verbose)
    
    # Print banner
    print("🚀 Daily Catalyst - AI-powered Meeting Analyzer")
    print("=" * 60)
    
    # Execute command
    success = False
    
    if args.command == 'pipeline':
        success = full_pipeline(args)
    elif args.command == 'embeddings':
        success = create_embeddings(args)
    elif args.command == 'transcript':
        success = create_transcript(args)
    elif args.command == 'analyze':
        success = analyze_meeting(args)
    elif args.command == 'realtime':
        success = run_realtime(args)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
