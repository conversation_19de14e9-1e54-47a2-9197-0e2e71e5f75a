#!/usr/bin/env python3
"""
Live Real-Time Transcription & Analysis

This script captures audio from a system audio source in real-time,
fed by a video player (or any other application), and transcribes it live
while performing off-topic analysis.
"""

import asyncio
import sys
import os
import logging
from pathlib import Path
from dotenv import load_dotenv

# --- Setup ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
load_dotenv()
sys.path.append('.')
sys.path.append('src')

# --- Imports ---
try:
    from deepgram_live_demo import DeepgramOfficialTest, RealTimeOffTopicAnalyzer
    from deepgram import LiveTranscriptionEvents, LiveOptions, DeepgramClient, DeepgramClientOptions
except ImportError as e:
    logging.error(f"❌ Import error: {e}. Please ensure all dependencies are installed.")
    sys.exit(1)

# --- Constants ---
# Use the RDP sink monitor if available, otherwise default to PulseAudio's default
AUDIO_SOURCE_NAME = os.getenv("AUDIO_SOURCE", "RDPSink.monitor")


class LiveVideoPlayer:
    """Plays a video using the default audio output."""
    
    def __init__(self, video_path: str):
        self.video_path = video_path
        self.player_process = None
        logging.info(f"⚡ Live video player initialized for: {Path(video_path).name}")

    async def play_video(self):
        """Play video using the default audio device."""
        cmd = ['ffplay', '-autoexit', '-loglevel', 'quiet', self.video_path]
        logging.info("⚡ Starting video playback...")
        self.player_process = await asyncio.create_subprocess_exec(
            *cmd, stdout=asyncio.subprocess.DEVNULL, stderr=asyncio.subprocess.DEVNULL
        )
        await self.player_process.wait()
        logging.info("⚡ Video playback finished.")

    async def stop(self):
        if self.player_process and self.player_process.returncode is None:
            self.player_process.terminate()
            await self.player_process.wait()


class LiveAudioStreamer:
    """Captures audio from the specified audio source and streams it."""

    def __init__(self, transcriber, config_loader=None):
        self.transcriber = transcriber
        self.ffmpeg_process = None

        # Load configuration
        if config_loader is None:
            from config.config_loader import get_config_loader
            config_loader = get_config_loader()
        self.config_loader = config_loader

    async def start_streaming(self):
        """Start capturing and streaming audio with detailed logging."""
        # Get audio settings from configuration
        input_format = self.config_loader.get_setting('audio.ffmpeg.input_format', 'pulse')
        output_format = self.config_loader.get_setting('audio.ffmpeg.output_format', 's16le')
        codec = self.config_loader.get_setting('audio.ffmpeg.codec', 'pcm_s16le')
        sample_rate = self.config_loader.get_setting('audio.ffmpeg.sample_rate', 16000)
        channels = self.config_loader.get_setting('audio.ffmpeg.channels', 1)
        flush_packets = self.config_loader.get_setting('audio.ffmpeg.flush_packets', True)
        chunk_size = self.config_loader.get_setting('audio.streaming.chunk_size', 2048)

        cmd = [
            'ffmpeg', '-f', input_format, '-i', AUDIO_SOURCE_NAME,
            '-f', output_format, '-acodec', codec, '-ar', str(sample_rate), '-ac', str(channels),
            '-flush_packets', '1' if flush_packets else '0', 'pipe:1'
        ]
        logging.info(f"🎤 Starting to listen on {AUDIO_SOURCE_NAME}...")
        
        self.ffmpeg_process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE  # Capture stderr
        )

        async def log_stderr():
            """Read and log ffmpeg's stderr."""
            while True:
                line = await self.ffmpeg_process.stderr.readline()
                if not line:
                    break
                # logging.info(f"[ffmpeg stderr] {line.decode().strip()}")  # Too verbose

        async def read_stdout():
            """Read audio chunks from ffmpeg's stdout."""
            while True:
                chunk = await self.ffmpeg_process.stdout.read(chunk_size)
                if not chunk:
                    logging.info("ffmpeg stdout stream ended.")
                    break
                # logging.info(f"Read audio chunk of size: {len(chunk)}")  # Too verbose
                self.transcriber.send(chunk)

        try:
            stderr_task = asyncio.create_task(log_stderr())
            stdout_task = asyncio.create_task(read_stdout())
            
            await asyncio.gather(stderr_task, stdout_task)

        except asyncio.CancelledError:
            logging.info("Audio streaming cancelled.")
        finally:
            logging.info("Audio streaming stopped.")
            if self.ffmpeg_process and self.ffmpeg_process.returncode is None:
                self.ffmpeg_process.terminate()
                await self.ffmpeg_process.wait()

    async def stop(self):
        if self.ffmpeg_process and self.ffmpeg_process.returncode is None:
            self.ffmpeg_process.terminate()
            try:
                await asyncio.wait_for(self.ffmpeg_process.wait(), timeout=5.0)
            except asyncio.TimeoutError:
                logging.warning("FFmpeg process didn't terminate gracefully, killing...")
                self.ffmpeg_process.kill()
                await self.ffmpeg_process.wait()


class LiveTranscriber(DeepgramOfficialTest):
    """Adapted version of DeepgramOfficialTest for live audio streams."""

    def __init__(self, deepgram_api_key: str, gemini_api_key: str, config_loader=None):
        # We don't have a file, so we pass None
        super().__init__(deepgram_api_key, gemini_api_key, audio_file=None, config_loader=config_loader)

    def send(self, chunk: bytes):
        """Sends a chunk of audio data to the Deepgram connection."""
        if self.dg_connection:
            try:
                self.dg_connection.send(chunk)
            except Exception as e:
                logging.error(f"Error sending chunk to Deepgram: {e}")

async def run_live_transcription(video_path: str):
    """Run the live transcription and analysis process."""
    # Load configuration
    from config.config_loader import get_config_loader
    config_loader = get_config_loader()
    config = config_loader.load_all_configs()

    if not config.is_valid:
        logging.warning("⚠️ Configuration has errors, using fallback settings:")
        for error in config.errors:
            logging.warning(f"   - {error}")

    deepgram_key = os.getenv('DEEPGRAM_API_KEY')
    gemini_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')

    if not deepgram_key or not gemini_key:
        logging.error("❌ Missing API keys. Please set DEEPGRAM_API_KEY and GEMINI_API_KEY.")
        return

    transcriber = LiveTranscriber(deepgram_key, gemini_key, config_loader)
    video_player = LiveVideoPlayer(video_path)
    audio_streamer = LiveAudioStreamer(transcriber, config_loader)

    try:
        if not transcriber.setup_connection():
            logging.error("Failed to set up Deepgram connection.")
            return

        await asyncio.sleep(1) # Wait for connection to be ready

        video_task = asyncio.create_task(video_player.play_video())
        await asyncio.sleep(0.5)  # Give the player time to activate the audio sink
        streamer_task = asyncio.create_task(audio_streamer.start_streaming())

        # Wait for video to finish, then cancel audio streaming
        await video_task
        logging.info("Video finished, stopping audio streaming...")
        streamer_task.cancel()

        try:
            await streamer_task
        except asyncio.CancelledError:
            logging.info("Audio streaming cancelled successfully.")

    except KeyboardInterrupt:
        logging.info("\n⏹️ Stopped by user")
    except Exception as e:
        logging.error(f"An unexpected error occurred: {e}")
    finally:
        logging.info("Cleaning up resources...")
        await video_player.stop()
        await audio_streamer.stop()
        transcriber.cleanup()
        logging.info("Cleanup complete.")

def main():
    import argparse
    parser = argparse.ArgumentParser(description="Live Real-Time Video Transcription and Analysis")
    parser.add_argument("video", help="Path to video file to simulate a real-time stream")
    args = parser.parse_args()

    video_path = Path(args.video)
    if not video_path.exists():
        logging.error(f"❌ Video file not found: {video_path}")
        return 1

    try:
        asyncio.run(run_live_transcription(str(video_path)))
    except KeyboardInterrupt:
        logging.info("\n👋 Goodbye!")
    return 0

if __name__ == "__main__":
    sys.exit(main())