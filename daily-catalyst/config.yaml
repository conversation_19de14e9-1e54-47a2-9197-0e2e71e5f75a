# Daily Catalyst Configuration

# Audio processing settings
audio:
  sample_rate: 16000
  chunk_duration: 4.0  # seconds
  min_chunk_duration: 1.0  # minimum chunk duration
  max_chunk_duration: 10.0  # maximum chunk duration

# Speech recognition settings
speech_recognition:
  language: "en-us"  # or "ru-ru"
  method: "speechrecognition_pyannote"
  buffer_duration: 1.0
  confidence_threshold: 0.5

# Speaker identification settings
speaker_identification:
  method: "embeddings"  # embeddings, diarization, simple
  similarity_threshold: 0.7
  adaptive_threshold: 0.7
  base_threshold: 0.6
  min_speakers: 2
  max_speakers: 8
  
  # Embeddings settings
  embeddings:
    model: "speechbrain/spkrec-ecapa-voxceleb"
    embedding_size: 192
    min_segment_duration: 1.0
    max_segments_per_speaker: 10

# Meeting analysis settings
analysis:
  # Gemini AI settings for off-topic detection
  gemini:
    model: "gemini-2.0-flash-exp"  # Gemini Flash 2.5 model
    api_key_env: "GEMINI_API_KEY"  # Environment variable name
    timeout: 30  # seconds
    max_retries: 3

  # Off-topic detection (legacy keyword-based)
  off_topic:
    threshold: 0.6
    keywords:
      standup: ["yesterday", "today", "tomorrow", "working on", "completed", "blocked"]
      deviation: ["by the way", "speaking of", "off topic", "efficiency", "meeting"]
      technical: ["implementation", "algorithm", "debugging", "refactoring", "api"]
      complaint: ["problem", "issue", "trouble", "shit", "pretending"]

  # Deep dive detection
  deep_dive:
    duration_threshold: 60  # seconds
    technical_word_threshold: 2

  # Quality scoring
  quality:
    max_duration: 900  # 15 minutes
    optimal_duration: 600  # 10 minutes
    efficiency_weight: 0.4
    participation_weight: 0.3
    format_compliance_weight: 0.3

# Output settings
output:
  save_transcripts: true
  save_analysis: true
  save_embeddings: true
  
  formats:
    - "txt"
    - "json"
    - "csv"
  
  # File naming
  timestamp_format: "%Y%m%d_%H%M%S"
  
  # Directories
  transcripts_dir: "transcripts"
  analysis_dir: "analysis"
  embeddings_dir: "embeddings"
  reports_dir: "reports"

# Logging settings
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  file: "daily_catalyst.log"
  max_size: "10MB"
  backup_count: 5

# Performance settings
performance:
  max_workers: 4
  chunk_overlap: 0.5  # seconds
  memory_limit: "2GB"
  
# Real-time settings
realtime:
  enable_live_display: true
  update_interval: 1.0  # seconds
  buffer_size: 5.0  # seconds

# Development settings
development:
  debug_mode: false
  save_intermediate_files: false
  profile_performance: false
