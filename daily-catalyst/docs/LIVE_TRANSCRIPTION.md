# Live Real-Time Transcription Guide

## Overview

The `live_transcription.py` script provides real-time video transcription with speaker identification and off-topic detection. It captures audio from video playback and processes it in real-time using Deepgram and Google Gemini APIs.

## Features

- **Real-time transcription**: Live speech-to-text with minimal delay
- **Speaker diarization**: Automatic speaker identification and separation
- **Off-topic detection**: Real-time analysis using multiple AI models
- **Multi-model fallback**: Automatic switching between AI models when quotas exceeded
- **Video support**: Works with any video format supported by FFmpeg
- **Clean output**: Formatted transcription with timestamps and quality indicators

## Quick Start

### 1. Setup

```bash
# Ensure virtual environment is activated
source .venv/bin/activate

# Set up API keys in .env file
DEEPGRAM_API_KEY=your_deepgram_api_key
GEMINI_API_KEY=your_gemini_api_key
```

### 2. Run Live Transcription

```bash
# Basic usage with test video
python live_transcription.py test_video/meeting_video.mp4

# With your own video file
python live_transcription.py path/to/your/meeting.mp4
```

## System Requirements

### Software Dependencies
- Python 3.8+
- FFmpeg (for video playback and audio capture)
- PulseAudio (for audio routing)

### API Requirements
- **Deepgram API**: For speech-to-text transcription
  - Get free $200 credits at: https://console.deepgram.com/
- **Google Gemini API**: For off-topic analysis
  - Get free API key at: https://makersuite.google.com/app/apikey
  - **Multi-model support**: Uses Gemini 2.5 Flash, Gemini 2.0 Flash, and Gemma 3n-e2b

### Audio Setup
The script captures audio from system audio output. Default source is `RDPSink.monitor`.

To change audio source:
```bash
export AUDIO_SOURCE="your_audio_source_name"
python live_transcription.py your_video.mp4
```

## How It Works

### Architecture
1. **Video Player**: FFplay plays video with audio output
2. **Audio Capture**: FFmpeg captures system audio in real-time
3. **Transcription**: Deepgram processes audio chunks via WebSocket
4. **Analysis**: Gemini analyzes transcribed text for off-topic content
5. **Output**: Real-time display with speaker identification and quality indicators

### Audio Processing Pipeline
```
Video File → FFplay → System Audio → FFmpeg → Audio Chunks → Deepgram → Text
                                                                    ↓
                                                              Gemini Analysis
                                                                    ↓
                                                            Formatted Output
```

## Multi-Model Fallback System

### Automatic Model Switching
The system uses a three-tier fallback approach to ensure continuous analysis even when API quotas are exceeded:

1. **Primary Model**: Gemini 2.5 Flash
   - Best accuracy for off-topic detection
   - 8 RPM, 200 requests/day (conservative limits)

2. **Secondary Model**: Gemini 2.0 Flash
   - Good accuracy with higher quotas
   - 12 RPM, 150 requests/day

3. **Tertiary Model**: Gemma 3n-e2b
   - Basic analysis with highest quotas
   - 25 RPM, 1000 requests/day

### How Fallback Works
```
Gemini 2.5 Flash (quota exceeded) → Gemini 2.0 Flash (quota exceeded) → Gemma 3n-e2b → Local Fallback
```

### Visual Indicators
- `⏳ Gemini 2.5 Flash analyzing...` - Primary model in use
- `🔄 Switched to Gemini 2.0 Flash (quota available)` - Automatic fallback
- `💰 All models exhausted - using local fallback` - All quotas exceeded

## Output Format

### Real-time Display
- `✅` - On-topic content
- `🚨` - Off-topic content with reason
- `[timestamp]` - Time in seconds from start
- `Speaker N` - Identified speaker

### Example Output
```
[25.4s] 🔄 Speaker 1: then another meeting about improving our meeting efficiency.
         ⏳ Gemini 2.5 Flash analyzing...
[25.4s] 🤖 AI RESULT: 🚨 OFF-TOPIC
         ⚠️  Meta-discussion about process

[91.2s] 🔄 Speaker 3: Which had a width of 0 because of a missing prop.
         ⏳ Gemini 2.0 Flash analyzing...
[91.2s] 🤖 AI RESULT: 🚨 OFF-TOPIC
         ⚠️  Technical implementation detail

🔄 Switched to Gemma 3n-e2b (quota available)
```

### Final Statistics
```
📊 Transcription Stats:
   • Final transcripts: 55
   • Duration: 157.4s
🤖 Analysis Stats:
   • Off-topic segments: 2
   • Off-topic ratio: 3.6%
🎯 Meeting Quality: Excellent focus!
```

## Off-Topic Detection

### Categories Detected
1. **Excessive Technical Detail**: Deep implementation discussions
2. **Problem-Solving Attempts**: Live debugging during standup
3. **Meta-Discussions**: Process improvement conversations
4. **Lengthy Excuses**: Detailed explanations beyond brief updates
5. **Irrelevant Information**: Non-work related content

### Quality Assessment
- **< 10% off-topic**: Excellent focus!
- **10-20% off-topic**: Good discipline
- **> 20% off-topic**: Needs improvement

## Troubleshooting

### Common Issues

**No audio captured**
```bash
# Check available audio sources
pactl list sources short

# Set correct audio source
export AUDIO_SOURCE="your_source_name"
```

**FFmpeg not found**
```bash
# Install FFmpeg
sudo apt update
sudo apt install ffmpeg
```

**API connection issues**
- Verify API keys in `.env` file
- Check internet connection
- Ensure API quotas are not exceeded

**Poor transcription quality**
- Ensure clear audio in video
- Check audio levels are adequate
- Verify video has audio track

### Performance Optimization

**Reduce latency**
- Use smaller audio chunks (modify chunk size in code)
- Ensure stable internet connection
- Use faster hardware

**Improve accuracy**
- Use high-quality audio sources
- Minimize background noise
- Ensure clear speaker separation

## Configuration

### Environment Variables
```bash
# Audio source (default: RDPSink.monitor)
AUDIO_SOURCE=your_audio_source

# API keys (required)
DEEPGRAM_API_KEY=your_key
GEMINI_API_KEY=your_key

# Optional: Debug mode
DEBUG=true
```

### Audio Parameters
- **Sample Rate**: 16kHz (optimized for speech)
- **Channels**: Mono (1 channel)
- **Format**: 16-bit signed little-endian
- **Chunk Size**: 2048 bytes

## Best Practices

### For Best Results
1. **Audio Quality**: Use clear, noise-free audio
2. **Speaker Separation**: Ensure speakers don't overlap
3. **Meeting Format**: Follow structured standup format
4. **Video Quality**: Use videos with clear audio tracks

### Meeting Guidelines
1. **Speak Clearly**: Enunciate for better transcription
2. **Avoid Overlap**: Wait for others to finish
3. **Stay On-Topic**: Follow standup format (yesterday, today, blockers)
4. **Be Concise**: Keep updates brief and focused

## Integration

### With CI/CD
```bash
# Automated meeting analysis
python live_transcription.py recorded_meeting.mp4 > meeting_analysis.txt
```

### With Other Tools
- Export transcripts for further analysis
- Integrate with meeting management systems
- Use for team performance metrics

## Limitations

- **Live API Constraints**: Deepgram Live API has limited speaker diarization
- **Internet Dependency**: Requires stable internet for real-time processing
- **Audio Source**: Depends on system audio configuration
- **Video Format**: Limited to formats supported by FFmpeg

## Support

For issues or questions:
1. Check troubleshooting section
2. Verify system requirements
3. Test with provided sample video
4. Check API key configuration
