# Real-Time Off-Topic Detection с Google Gemini API

## 🎯 Обзор

Daily Catalyst включает **революционную систему real-time анализа** отхода от темы в ежедневных стендап-митингах. Используя Google Gemini Flash 2.5, система анализирует **каждую фразу в реальном времени** с учётом **полного контекста митинга с самого начала**, применяя **профессиональные стандарты Scrum Master** для максимальной точности.

## ✨ Революционные возможности

- **🔥 Real-time анализ** каждой фразы во время live транскрипции
- **🧠 Полный контекст** - система помнит весь митинг с начала
- **👨‍💼 Профессиональные стандарты** строгого Scrum Master
- **🎯 Высокая точность** обнаружения технических углублений
- **📊 Live визуализация** (🚨 off-topic, ✅ on-topic)
- **⚡ Мгновенная обратная связь** участникам митинга
- **📈 Контекстно-зависимые решения** на основе фазы митинга
- **🏷️ Автоматическая разметка** OFF_TOPIC секций
- **💾 Детальная отчётность** с временными метками и причинами

## 🚀 Быстрый старт

### 1. Настройка API ключа

Получите бесплатный API ключ Gemini:
```bash
# Перейдите на: https://makersuite.google.com/app/apikey
# Добавьте ключ в .env файл:
echo "GEMINI_API_KEY=your_api_key_here" >> .env
```

### 2. Установка зависимостей

```bash
pip install google-generativeai
```

### 3. Запуск анализа

#### 🔥 Real-Time Live Analysis (Рекомендуется!)
```bash
# Запуск live транскрипции с real-time анализом
python deepgram_live_demo.py
```

#### Полный пайплайн
```bash
python daily_catalyst.py pipeline --audio-file meeting.wav
```

#### Тест с видео
```bash
python optimized_fast_video.py test_video/meeting_video.mp4
```

#### Анализ существующего транскрипта
```bash
python daily_catalyst.py analyze --transcript-file transcript.txt
```

## 🔥 Real-Time Live Analysis

### Как это работает:

1. **Live транскрипция** - Deepgram преобразует речь в текст в реальном времени
2. **Мгновенный анализ** - каждая новая фраза анализируется с Gemini AI
3. **Полный контекст** - система помнит весь митинг с самого начала
4. **Визуальная обратная связь** - участники видят результат мгновенно

### Пример real-time вывода:

```
🎙️  Live Transcription with Real-Time Off-Topic Analysis
=================================================================
[25.6s] 🚨 Speaker 1: then another meeting about improving our meeting efficiency.
         ⚠️  OFF-TOPIC: Meta-discussion about process

[88.1s] 🚨 Speaker 3: The flexbox was inside another flexbox.
         ⚠️  OFF-TOPIC: Excessive technical detail

[91.4s] 🚨 Speaker 3: Which had a width of 0 because of a missing prop.
         ⚠️  OFF-TOPIC: Excessive technical detail

[95.0s] ✅ Speaker 1: Let's not dive into technical stuff.
```

### Преимущества real-time анализа:

- **⚡ Мгновенная обратная связь** - участники сразу видят когда отходят от темы
- **🧠 Накопление контекста** - каждое решение основано на всей истории митинга
- **📈 Адаптивная строгость** - позже в митинге система становится строже
- **🎯 Высокая точность** - полный контекст обеспечивает лучшие решения

## 📋 Примеры использования

### Пример 1: Обнаружение технических углублений

**Входной транскрипт:**
```
[32.1s] Speaker 4: Yesterday I was debugging the CSS layout. The issue was with flexbox - specifically the justify-content property wasn't working because the parent container didn't have display: flex set properly...
```

**Результат анализа:**
```
[32.1s] Speaker 4: Yesterday I was debugging the CSS layout. <OFF_TOPIC>The issue was with flexbox - specifically the justify-content property wasn't working because the parent container didn't have display: flex set properly...</OFF_TOPIC>
```

### Пример 2: Архитектурные дискуссии

**Входной транскрипт:**
```
[82.4s] Speaker 2: Speaking of deployment, should we use MongoDB or PostgreSQL? And what about caching strategies? Redis vs Memcached?
```

**Результат анализа:**
```
[82.4s] Speaker 2: Speaking of deployment, <OFF_TOPIC>should we use MongoDB or PostgreSQL? And what about caching strategies? Redis vs Memcached?</OFF_TOPIC>
```

## 🔧 Конфигурация

### config.yaml
```yaml
analysis:
  gemini:
    model: "gemini-2.0-flash-exp"  # Gemini Flash 2.5
    api_key_env: "GEMINI_API_KEY"
    timeout: 30
    max_retries: 3
```

### .env файл
```bash
# Google Gemini API для обнаружения отхода от темы
GEMINI_API_KEY=your_gemini_api_key_here
```

## 📊 Отчёты и результаты

### Консольный вывод
```
📊 DAILY MEETING ANALYSIS SUMMARY
====================================
🤖 Model: gemini-2.0-flash-exp
⏱️  Analysis time: 2.34s
📄 Total segments: 12
🚨 Off-topic segments: 2
📈 Off-topic ratio: 16.7%

🚨 OFF-TOPIC SEGMENTS DETECTED:
----------------------------------------
1. Technical deep-dive detected by Gemini AI
   👤 Speaker: 4
   ⏰ Time: 32.1s
   💬 Content: The issue was with flexbox - specifically...
```

### JSON отчёт
```json
{
  "analysis_metadata": {
    "model_used": "gemini-2.0-flash-exp",
    "analysis_time": 2.34,
    "timestamp": "2024-12-14 15:30:45",
    "total_segments": 12,
    "off_topic_count": 2
  },
  "off_topic_segments": [
    {
      "content": "The issue was with flexbox...",
      "speaker": "4",
      "timestamp": "32.1",
      "reason": "Technical deep-dive detected by Gemini AI"
    }
  ]
}
```

## 🎯 Что определяется как off-topic

Gemini AI обучен выявлять:

### ✅ Подходящее для стендапа
- Прогресс за вчера
- Планы на сегодня
- Блокировки и препятствия
- Краткие статусы задач

### 🚨 Off-topic (неподходящее)
- **Технические детали**: конкретные строчки кода, CSS свойства
- **Отладка**: пошаговые процессы debugging
- **Архитектурные решения**: выбор технологий, дизайн системы
- **Реализация**: низкоуровневые детали implementation
- **Инструменты**: детальное обсуждение настроек и конфигураций

## 🔄 Интеграция с пайплайном

Off-topic анализ автоматически включается в полный пайплайн:

```bash
# Полный пайплайн включает:
# 1. Создание speaker embeddings
# 2. Транскрипция с speaker identification
# 3. Off-topic анализ с Gemini AI ← НОВОЕ!
# 4. Генерация отчётов

python daily_catalyst.py pipeline --audio-file meeting.wav
```

## 🧪 Тестирование

### Запуск анализа
```bash
# Основной скрипт для анализа видео
python optimized_fast_video.py test_video/meeting_video.mp4

# Анализ только аудио
python deepgram_live_demo.py
```

## 🔍 API Reference

### DailyMeetingAnalyzer

```python
from analysis.meeting_analyzer import DailyMeetingAnalyzer

# Инициализация
analyzer = DailyMeetingAnalyzer(
    transcript_file="transcript.txt",
    api_key="your_gemini_key"
)

# Анализ
success = analyzer.analyze_meeting()

# Результаты
analyzer.display_summary()
analyzer.save_analysis_report("report.json")
```

### Основные методы

- `analyze_meeting()` - Запуск анализа
- `display_summary()` - Показать результаты
- `save_analysis_report()` - Сохранить отчёт
- `load_transcript()` - Загрузить транскрипт

## 💡 Советы по использованию

### Для лучших результатов:
1. **Качественные транскрипты** - используйте чёткие записи
2. **Структурированные митинги** - следуйте формату стендапа
3. **Регулярный анализ** - анализируйте митинги постоянно

### Интерпретация результатов:
- **0-10% off-topic** - Отличная фокусировка
- **10-20% off-topic** - Хорошая дисциплина
- **20%+ off-topic** - Нужно улучшить формат

## 🔗 Полезные ссылки

- [Google AI Studio](https://makersuite.google.com/) - Получить API ключ
- [Gemini API Docs](https://ai.google.dev/docs) - Документация API
- [Daily Catalyst Docs](../README.md) - Основная документация

## 🆘 Troubleshooting

### Частые проблемы:

**❌ "GEMINI_API_KEY not found"**
```bash
# Решение: добавьте ключ в .env
echo "GEMINI_API_KEY=your_key" >> .env
```

**❌ "Import error: google.generativeai"**
```bash
# Решение: установите зависимость
pip install google-generativeai
```

**❌ "Empty response from Gemini API"**
- Проверьте интернет соединение
- Убедитесь что API ключ действителен
- Проверьте лимиты API

### Логи и отладка:
```bash
# Запуск с подробными логами
python daily_catalyst.py analyze --transcript-file transcript.txt --verbose
```
