#!/usr/bin/env python3
"""
Optimized Fast Video Transcription

Правильная оптимизация для быстрого отклика транскрипции:
- Оптимизированные чанки (200ms)
- Реальная скорость стриминга (1.0x)
- Минимальные задержки обработки
- Оптимизированные настройки Deepgram
"""

import asyncio
import subprocess
import time
import sys
import os
import tempfile
import logging
from pathlib import Path
from dotenv import load_dotenv

# --- Constants ---
VIDEO_START_DELAY = 0.3
FINAL_TRANSCRIPT_WAIT = 2.0
CHUNK_DURATION_MS = 200
PROGRESS_INTERVAL_S = 1.0

# --- Setup ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
load_dotenv()
sys.path.append('.')
sys.path.append('src')

try:
    from deepgram_live_demo import DeepgramOfficialTest
    from realtime_audio_streamer import RealTimeAudioStreamer
except ImportError as e:
    logging.error(f"❌ Import error: {e}")
    sys.exit(1)


class OptimizedVideoPlayer:
    """Optimized video player using asyncio for better integration."""
    
    def __init__(self, video_path: str):
        self.video_path = video_path
        self.player_process = None
        self.start_time = None
        logging.info(f"⚡ Optimized video player initialized for: {Path(video_path).name}")

    async def play_video_optimized(self):
        """Play video with optimized settings using asyncio."""
        cmd = [
            'ffplay',
            '-autoexit',
            '-loglevel', 'quiet',
            '-fast',  # Fast decoding
            self.video_path
        ]
        
        logging.info("⚡ Starting optimized video playback...")
        
        try:
            self.start_time = time.time()
            
            self.player_process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.DEVNULL,
                stderr=asyncio.subprocess.DEVNULL
            )
            
            await self.player_process.wait()
            
            duration = time.time() - self.start_time
            logging.info(f"⚡ Video completed in {duration:.1f}s")
            
        except Exception as e:
            logging.error(f"❌ Video error: {e}")
        finally:
            self.player_process = None

    def get_elapsed_time(self):
        """Get elapsed time since video playback started."""
        if self.start_time and self.is_playing():
            return time.time() - self.start_time
        return 0

    def is_playing(self):
        """Check if the video is currently playing."""
        return self.player_process is not None and self.player_process.returncode is None

    async def stop_video(self):
        """Stop the video player."""
        if not self.is_playing():
            return

        try:
            self.player_process.terminate()
            await asyncio.wait_for(self.player_process.wait(), timeout=1.0)
        except asyncio.TimeoutError:
            self.player_process.kill()
            await self.player_process.wait()
        except Exception as e:
            logging.error(f"Error stopping video: {e}")
        finally:
            self.player_process = None


class OptimizedAudioExtractor:
    """Optimized audio extraction."""
    
    def __init__(self, video_path: str):
        self.video_path = video_path
        self.temp_audio_file = None
        logging.info("⚡ Optimized audio extractor initialized")
    
    def extract_audio_optimized(self):
        """Extract audio with optimization."""
        try:
            temp_dir = tempfile.gettempdir()
            self.temp_audio_file = os.path.join(temp_dir, f"optimized_{int(time.time())}.wav")
            
            logging.info("⚡ Optimized audio extraction...")
            
            cmd = [
                'ffmpeg',
                '-i', self.video_path,
                '-vn', '-acodec', 'pcm_s16le', '-ar', '16000', '-ac', '1',
                '-threads', '0',
                self.temp_audio_file,
                '-y', '-loglevel', 'quiet'
            ]
            
            start_time = time.time()
            subprocess.run(cmd, check=True)
            extraction_time = time.time() - start_time
            
            if os.path.exists(self.temp_audio_file):
                file_size = os.path.getsize(self.temp_audio_file)
                logging.info(f"⚡ Extraction: {extraction_time:.2f}s, {file_size} bytes")
                return True
            return False
            
        except Exception as e:
            logging.error(f"❌ Extraction error: {e}")
            return False
    
    def cleanup(self):
        """Cleanup temporary audio file."""
        if self.temp_audio_file and os.path.exists(self.temp_audio_file):
            try:
                os.remove(self.temp_audio_file)
                logging.info(f"🗑️ Cleaned up: {self.temp_audio_file}")
            except OSError as e:
                logging.warning(f"Could not remove temp file: {e}")


class OptimizedTranscriber:
    """Optimized transcriber for maximum responsiveness."""
    
    def __init__(self, deepgram_api_key: str, gemini_api_key: str, audio_file: str):
        self.audio_file = audio_file
        self.transcriber = DeepgramOfficialTest(deepgram_api_key, gemini_api_key, audio_file)
        logging.info("⚡ Optimized transcriber initialized")
    
    async def run_optimized_transcription(self, video_player: OptimizedVideoPlayer):
        """Run optimized transcription with maximum responsiveness."""
        try:
            logging.info("⚡ Setting up optimized transcription...")
            
            if not self.transcriber.setup_connection():
                logging.error("❌ Failed to setup connection")
                return False
            
            logging.info("✅ Connection established")
            await asyncio.sleep(0.5)
            
            streamer = RealTimeAudioStreamer(
                chunk_duration_ms=CHUNK_DURATION_MS,
                speed_multiplier=1.0
            )

            logging.info(f"⚡ OPTIMIZED streaming settings: Chunk size: {CHUNK_DURATION_MS}ms, Speed: 1.0x")
            
            if not streamer.load_audio_file(self.audio_file):
                logging.error("❌ Failed to load audio")
                return False
            
            chunk_count = 0
            last_progress_time = 0
            
            def on_optimized_chunk(chunk_bytes: bytes, start_time: float, end_time: float):
                nonlocal chunk_count, last_progress_time
                if self.transcriber.dg_connection:
                    try:
                        self.transcriber.dg_connection.send(chunk_bytes)
                        chunk_count += 1
                        
                        current_time = time.time()
                        if current_time - last_progress_time >= PROGRESS_INTERVAL_S:
                            video_time = video_player.get_elapsed_time()
                            print(f"\r⚡ OPTIMIZED: Video {video_time:.1f}s | Audio {end_time:.1f}s | {chunk_count} chunks", end='', flush=True)
                            last_progress_time = current_time
                    except Exception as e:
                        logging.error(f"\n❌ Error sending chunk: {e}")
            
            streamer.on_audio_chunk = on_optimized_chunk
            
            logging.info("🚀 Starting OPTIMIZED real-time streaming...")
            
            success = await streamer.start_streaming()
            
            if success:
                print(f"\n⚡ OPTIMIZED streaming completed: {chunk_count} chunks")
            else:
                print(f"\n⚠️ OPTIMIZED streaming issues: {chunk_count} chunks")
            
            return success
            
        except Exception as e:
            logging.error(f"❌ Optimized transcription error: {e}")
            return False
    
    def cleanup(self):
        """Cleanup transcriber resources."""
        if self.transcriber:
            self.transcriber.cleanup()


async def run_optimized_fast_video_transcription(video_path: str):
    """Run optimized fast video transcription."""
    
    logging.info("⚡ Daily Catalyst - OPTIMIZED Fast Video Transcription")
    
    deepgram_key = os.getenv('DEEPGRAM_API_KEY')
    gemini_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
    
    if not deepgram_key or not gemini_key:
        logging.error("❌ Missing API keys")
        return False
    
    logging.info("✅ API keys ready")
    
    video_player = OptimizedVideoPlayer(video_path)
    audio_extractor = OptimizedAudioExtractor(video_path)
    transcriber = None
    
    try:
        logging.info("⚡ Step 1: Optimized audio extraction...")
        if not audio_extractor.extract_audio_optimized():
            return False
        
        transcriber = OptimizedTranscriber(deepgram_key, gemini_key, audio_extractor.temp_audio_file)
        
        logging.info("⚡ Step 2: Start optimized video and transcription...")
        
        video_task = asyncio.create_task(video_player.play_video_optimized())
        
        await asyncio.sleep(VIDEO_START_DELAY)
        
        transcription_task = asyncio.create_task(transcriber.run_optimized_transcription(video_player))
        
        await asyncio.gather(video_task, transcription_task)
        
        logging.info("\n⚡ Optimized final processing...")
        await asyncio.sleep(FINAL_TRANSCRIPT_WAIT)
        
        return transcription_task.result()
        
    except KeyboardInterrupt:
        logging.info("\n⏹️ Stopped by user")
        return True
    except Exception as e:
        logging.error(f"❌ Optimized error: {e}")
        return False
    finally:
        if video_player:
            await video_player.stop_video()
        if transcriber:
            transcriber.cleanup()
        if audio_extractor:
            audio_extractor.cleanup()


def main():
    """Main function."""
    
    import argparse
    parser = argparse.ArgumentParser(description="Optimized Fast Video Transcription")
    parser.add_argument("video", help="Path to video file")
    
    args = parser.parse_args()
    
    video_path = Path(args.video)
    if not video_path.exists():
        logging.error(f"❌ Video file not found: {video_path}")
        return 1
    
    try:
        success = asyncio.run(run_optimized_fast_video_transcription(str(video_path)))
        
        if success:
            logging.info("\n⚡ OPTIMIZED fast video transcription completed!")
        else:
            logging.warning("\n⚠️ Optimized transcription had issues")
        
        return 0
        
    except KeyboardInterrupt:
        logging.info("\n⏹️ Stopped by user")
        return 1
    except Exception as e:
        logging.error(f"\n❌ Error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())