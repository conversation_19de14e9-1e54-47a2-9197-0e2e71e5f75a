# Daily Catalyst

🎙️ **Real-time video transcription with AI-powered off-topic detection for meetings**

Transform your meeting videos into live transcripts with instant off-topic analysis. Perfect for daily standups, team meetings, and productivity monitoring.

## ⚡ Quick Start

### 1. Prerequisites

- Python 3.8+
- [Deepgram API key](https://deepgram.com) (for transcription)
- [Google Gemini API key](https://aistudio.google.com) (for off-topic analysis)

### 2. Installation

```bash
git clone <repository-url>
cd daily-catalyst
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 3. Set API Keys

```bash
export DEEPGRAM_API_KEY="your_deepgram_api_key_here"
export GEMINI_API_KEY="your_gemini_api_key_here"
```

### 4. Configuration (Optional)

The system uses configurable prompts and settings. Default configuration works out of the box, but you can customize:

```bash
# View current configuration
ls config/
# prompts.yaml    - AI prompts for off-topic detection
# settings.yaml   - Technical parameters (API, audio, analysis)
# config.yaml     - Main application settings
```

### 5. Run Real-Time Analysis

```bash
# Live transcription with off-topic detection (Recommended)
python live_transcription.py test_video/meeting_video.mp4

# Alternative: Optimized video processing
python optimized_fast_video.py test_video/meeting_video.mp4
```

## 🎯 What You Get

**Live Transcription:**
```
[0.4s] ✅ Speaker 0: Good morning, my dear team.
[2.6s] ✅ Speaker 0: Let's start the stand up...
[7.2s] ✅ Speaker 0: What have you done yesterday?
[13.5s] ✅ Speaker 1: So yesterday, mostly meeting.
```

**Off-Topic Detection:**
```
[45.2s] 🚨 Speaker 1: Let me explain how CSS flexbox works...
         ⚠️  OFF_TOPIC: Excessive technical details not relevant to standup format
```

**Meeting Quality Analysis:**
```
📊 Final Statistics:
   • Total segments: 55
   • Off-topic segments: 3 (5.5%)
   • Meeting Quality: Excellent focus!
```

## 🚀 Features

- **⚡ Ultra-fast response** - 10ms chunks for minimal delay
- **🎬 Video playback** - Watch video while getting live transcription
- **🎙️ Speaker identification** - Automatic speaker diarization
- **🤖 AI analysis** - Real-time off-topic detection using Gemini Flash 2.5
- **📊 Quality metrics** - Meeting focus and efficiency analysis

## 🔧 API Keys Setup

### Deepgram (Transcription)
1. Go to [deepgram.com](https://deepgram.com)
2. Sign up and get your API key
3. Set: `export DEEPGRAM_API_KEY="your_key"`

### Google Gemini (AI Analysis)
1. Go to [aistudio.google.com](https://aistudio.google.com)
2. Get your API key
3. Set: `export GEMINI_API_KEY="your_key"`

## 📁 File Structure

```
daily-catalyst/
├── live_transcription.py     # 🎯 Live transcription (recommended!)
├── optimized_fast_video.py   # ⚡ Alternative video processing
├── test_video/              # 📹 Sample video for testing
├── deepgram_live_demo.py    # 🎙️ Transcription engine
├── src/realtime_audio_streamer.py  # ⚡ Audio streaming
├── docs/                    # 📚 Documentation
│   ├── LIVE_TRANSCRIPTION.md # 📖 Live transcription guide
│   └── USAGE.md             # 📖 General usage guide
└── requirements.txt         # 📦 Dependencies
```

## 🎬 Usage Examples

**Live transcription (recommended):**
```bash
# Activate environment and run live transcription
source .venv/bin/activate
python live_transcription.py test_video/meeting_video.mp4
```

**Alternative video processing:**
```bash
python optimized_fast_video.py your_meeting.mp4
```

**With your own video:**
```bash
python live_transcription.py path/to/your/meeting.mp4
```

## ✨ New Features

### Live Real-Time Transcription
- **Instant processing**: Real-time audio capture and transcription
- **Speaker identification**: Automatic speaker diarization
- **Clean termination**: Proper process cleanup after video ends
- **Quality metrics**: Meeting efficiency scoring

### Enhanced Off-Topic Detection
- **Technical deep-dives**: Detects excessive implementation details
- **Meta-discussions**: Identifies process improvement conversations
- **Problem-solving**: Catches live debugging attempts
- **Quality assessment**: Provides meeting focus ratings

### Multi-Model Fallback System
- **Automatic switching**: Seamlessly switches between AI models when quotas are exceeded
- **Three-tier system**: Gemini 2.5 Flash → Gemini 2.0 Flash → Gemma 3n-e2b
- **Smart quota management**: Tracks usage per model and switches intelligently
- **No interruption**: Continuous analysis even when primary model is exhausted

## 🎯 Perfect For

- **Daily standups** - Keep meetings focused and on-topic
- **Team meetings** - Real-time transcription and analysis
- **Meeting reviews** - Analyze meeting quality and efficiency
- **Productivity monitoring** - Track off-topic discussions

## 📊 Performance

- **Response time**: ~1-2 seconds delay
- **Chunk size**: 10ms (ultra-responsive)
- **Accuracy**: High-quality Deepgram Nova-2 model
- **Speed**: Real-time (1.0x video speed)

## 🆘 Troubleshooting

**No transcription appearing?**
- Check your Deepgram API key
- Ensure audio is audible in the video

**Off-topic analysis not working?**
- Verify your Gemini API key
- Check internet connection

**Video not playing?**
- Install ffplay: `sudo apt install ffmpeg`
- Check video file format (MP4 recommended)

## ⚙️ Configuration System

Daily Catalyst now features a flexible configuration system for easy customization:

### Configuration Files

- **`config/prompts.yaml`** - AI prompts for off-topic detection
- **`config/settings.yaml`** - Technical parameters (API, audio, analysis)
- **`config.yaml`** - Main application settings

### Customizing AI Prompts

Edit `config/prompts.yaml` to modify off-topic detection behavior:

```yaml
off_topic_detection:
  realtime_prompt: |
    Your custom prompt here...

  alternative_prompts:
    strict_mode: |
      Stricter analysis prompt...
```

### A/B Testing Prompts

Switch between different prompt versions:

```yaml
prompt_versions:
  current_version: "strict_architect"  # Change this to test different prompts
```

### Technical Settings

Modify `config/settings.yaml` for technical parameters:

```yaml
api:
  deepgram:
    model: "nova-3"
    language: "en-US"
  gemini:
    models:
      - name: "gemini-2.5-flash"
        rpm_limit: 8
        daily_limit: 200

audio:
  streaming:
    chunk_size: 2048
    chunk_duration_ms: 500
```

### Custom Prompts

Create custom prompts in `config/custom_prompts/` directory:

```bash
mkdir -p config/custom_prompts
echo "your_custom_prompt: |" > config/custom_prompts/my_prompts.yaml
echo "  Custom prompt content..." >> config/custom_prompts/my_prompts.yaml
```

### Backward Compatibility

All existing code continues to work without modification. Configuration is optional and provides fallback defaults.

## 📄 License

MIT License - see LICENSE file for details.
