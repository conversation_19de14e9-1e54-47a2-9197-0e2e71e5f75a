# API Integration Standards

**Type**: Auto
**Description**: Standards for integrating with external APIs including Gemini, ElevenLabs, Deepgram, and EPAM AI Proxy

## Gemini API Integration

- Use Google's VertexAI platform (API key: dial-lxvco7gj9vf9qtqfbvgm2i8v9w7) for accessing Gemini models
- Prefer gemini-2.5-flash model for AI functionality
- Use max_tokens=4000 for AI model configurations
- Implement proper quota management for all 39 available Gemini models:
  - gemini-1.0/1.5/2.0/2.5 variants
  - flash/pro/lite versions
  - experimental models
  - thinking models
  - TTS models
  - learnlm and gemma models
- Use gemini-1.5-flash-8b for real-time applications (2000 req/min quota)
- All models use 60s rolling window quota recovery
- Quotas restore at 1/60 of limit per second
- Handle 429 errors gracefully with proper fallback mechanisms

## ElevenLabs Voice Synthesis

- Use ElevenLabs v3 model instead of v2 for voice synthesis
- Implement proper API key handling for voice integration
- Support multiple languages with appropriate voice synthesis
- Ensure Russian voice synthesis without English accent for Russian language content
- Handle audio device configuration and playback properly

## Deepgram Integration

- Deepgram API key: ****************************************
- Use for voice agent technology when specifically required
- Pass API key properly to voice synthesis system
- Ensure audio devices and audio player are available for testing

## EPAM AI Proxy

- Use endpoint: https://ai-proxy.lab.epam.com/openai/models for model listing
- Use /v1/deployments/{model}/limits for limits checking
- Authenticate with Api-Key header
- Implement proper error handling for proxy-specific responses

## General API Best Practices

- Store all API keys in environment variables, never in code
- Implement comprehensive error handling for all API calls
- Use appropriate retry mechanisms with exponential backoff
- Monitor API usage and quota consumption
- Implement proper timeout handling for network operations
- Log API interactions for debugging while protecting sensitive data
- Use connection pooling and keep-alive connections where appropriate
- Implement proper rate limiting to respect API quotas
- Handle network failures gracefully with fallback options

## Error Handling Patterns

- Implement specific handling for quota exhaustion (429 errors)
- Provide meaningful error messages to users
- Log errors with sufficient context for debugging
- Implement circuit breaker patterns for unreliable services
- Use proper exception hierarchies for different error types
- Ensure graceful degradation when APIs are unavailable

## Security Considerations

- Never log API keys or sensitive authentication data
- Use HTTPS for all API communications
- Validate API responses to prevent injection attacks
- Implement proper authentication token refresh mechanisms
- Use secure storage for API credentials
- Rotate API keys regularly according to provider recommendations

## Performance Optimization

- Cache API responses where appropriate
- Use batch operations when available
- Implement proper connection management
- Monitor API response times and optimize accordingly
- Use compression when supported by APIs
- Implement proper pagination handling for large datasets
