# Project-Specific Guidelines for Daily Meeting Emulator

**Type**: Auto
**Description**: Specific guidelines for the daily meeting emulator project including meeting simulation, voice integration, and AI-powered dialogue generation

## Meeting Simulation Architecture

- Implement meeting dialogue generation with proper conversation flow
- Questions should be directed to specific participants by name
- Participants should generate responses to questions addressed to them specifically
- Moderator should actively manage meetings by selecting who speaks next
- Implement proper topic switching after moderator interventions
- Reset off-topic counters after moderator mentions need to stick to standup plan

## Dialogue Generation Rules

- Use real-time streaming dialogue generation with separate API requests for each phrase
- Generate each phrase based on current conversation context
- Avoid repetitive phrases and template responses
- Implement anti-repetition filtering to prevent participants from starting responses with same phrases
- Make AI-generated content more creative and original rather than using examples as templates
- No pauses between dialogue lines in meeting demos for faster playback

## Meeting Phases and Flow

### Discussion Phase
- Participants should talk among themselves while moderator only intervenes when needed
- Moderator should facilitate open discussion rather than requesting personal updates again
- Include more work-related content rather than just off-topic conversations
- Implement more off-topic segments in meeting dialogues

### Technical Discussions
- When detecting long technical discussions, moderator should suggest scheduling separate technical meetings
- Participants should discuss technical meeting proposals among themselves
- Participants should briefly agree to proposed attendees rather than providing detailed logistics
- After moderator interrupts suggesting meetings, participants should switch to different topics

### Jira Ticket Management
- When someone suggests creating a Jira ticket, moderator should offer to create it
- Provide brief title, suggest assignee, and ask for confirmation
- Base ticket assignments on logical expertise matching rather than arbitrary assignment
- When participants say they will create their own tickets, moderator should acknowledge without creating tickets for them
- Use shorter, conversational confirmations instead of repeating full ticket names
- Don't suggest tickets for every update - be selective about ticket creation

## Voice and Language Support

- Support multiple languages with language parameter configuration
- Use Russian voice synthesis without English accent for Russian language meetings
- Integrate ElevenLabs v3 for voice synthesis with proper API key handling
- Ensure audio devices and players are properly configured for voice functionality

## Configuration and Testing

- Externalize all meeting configurations to files for repeatability
- Support dynamic demo mode with real-time API generation
- Implement comprehensive testing of all 39 available Gemini models
- Use gemini-1.5-flash-8b for real-time applications with 2000 req/min quota
- All models use 60s rolling window quota recovery at 1/60 of limit per second

## Error Handling and Quota Management

- Handle Gemini API quota exhaustion (429 errors) gracefully
- Prevent infinite loops where same participant is called repeatedly
- Implement fallback mechanisms when AI generation fails
- Fix speech duration calculation to match real speech timing
- Monitor quota usage and implement efficient token management

## Meeting Summary and Reporting

- Display comprehensive meeting summaries at the end
- Include lists of created Jira tickets with assignees
- Show scheduled technical meetings with participant information
- Ensure demo scripts run to completion and display full summaries

## Console and UI Support

- Support dynamic process visualization in console with metrics and color coding
- Handle systems without rich formatting support
- Provide clear guidance for enabling console features when needed
