#!/usr/bin/env python3
"""
Model Switcher for ElevenLabs Demo
Allows switching between different ElevenLabs models
"""

import sys
import os

def switch_model(model_choice):
    """Switch the ElevenLabs model in the demo file"""
    
    models = {
        "1": ("eleven_turbo_v2_5", "Turbo v2.5 (balanced quality/speed)"),
        "2": ("eleven_flash_v2_5", "Flash v2.5 (ultra-fast, low latency)"),
        "3": ("eleven_multilingual_v2", "Multilingual v2 (highest quality)"),
    }
    
    if model_choice not in models:
        print("❌ Invalid choice!")
        return False
    
    model_id, description = models[model_choice]
    
    # Read the current file
    filename = "quick_dynamic_demo_epam_11labs.py"
    if not os.path.exists(filename):
        print(f"❌ File {filename} not found!")
        return False
    
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Replace the current model
    old_pattern = '"current": "'
    new_pattern = f'"current": "{model_id}"'
    
    # Find and replace the current model setting
    lines = content.split('\n')
    updated = False
    
    for i, line in enumerate(lines):
        if '"current":' in line and 'eleven_' in line:
            # Extract the current model
            start = line.find('"current": "') + len('"current": "')
            end = line.find('"', start)
            old_model = line[start:end]
            
            # Replace with new model
            lines[i] = line.replace(f'"current": "{old_model}"', f'"current": "{model_id}"')
            updated = True
            print(f"✅ Model switched from {old_model} to {model_id}")
            break
    
    if not updated:
        print("❌ Could not find model configuration to update!")
        return False
    
    # Write back to file
    with open(filename, 'w', encoding='utf-8') as f:
        f.write('\n'.join(lines))
    
    print(f"🎤 Now using: {description}")
    print(f"📁 Updated file: {filename}")
    return True

def main():
    print("🤖 ElevenLabs Model Switcher")
    print("=" * 40)
    print()
    print("Available models:")
    print("1. eleven_turbo_v2_5 (balanced quality/speed) - DEFAULT")
    print("2. eleven_flash_v2_5 (ultra-fast, ~75ms latency)")
    print("3. eleven_multilingual_v2 (highest quality)")
    print()
    
    if len(sys.argv) > 1:
        choice = sys.argv[1]
    else:
        choice = input("Choose model (1-3): ").strip()
    
    if switch_model(choice):
        print("\n✅ Model switch successful!")
        print("You can now run the demo with the new model:")
        print("python quick_dynamic_demo_epam_11labs.py")
    else:
        print("\n❌ Model switch failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
