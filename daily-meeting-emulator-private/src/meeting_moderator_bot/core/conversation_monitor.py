"""
Conversation Monitor - Tracks conversation state and flow

Monitors conversation dynamics, participant engagement, and meeting flow
to provide context for moderation decisions.
"""

import logging
import time
from typing import Dict, List, Optional, Deque
from dataclasses import dataclass, field
from collections import deque

logger = logging.getLogger(__name__)


@dataclass
class SpeechSegment:
    """Individual speech segment"""
    timestamp: float
    participant: str
    text: str
    duration: float
    word_count: int


@dataclass
class ConversationState:
    """Current state of the conversation"""
    current_speaker: Optional[str]
    current_speaker_start_time: float
    current_speaker_duration: float
    total_duration: float
    total_segments: int
    participants_spoken: List[str]
    context: str
    recent_topics: List[str]
    speaking_pattern: Dict[str, float]  # participant -> total speaking time


@dataclass
class MonitoringConfig:
    """Configuration for conversation monitoring"""
    context_window_size: int = 10  # Number of recent segments to keep in context
    max_context_length: int = 1000  # Maximum characters in context string
    speaking_time_threshold: float = 120.0  # 2 minutes
    engagement_tracking: bool = True
    topic_extraction: bool = True


class ConversationMonitor:
    """
    Monitors conversation flow and participant engagement
    
    Tracks speaking patterns, conversation context, and meeting dynamics
    to provide insights for moderation decisions.
    """
    
    def __init__(self, config: MonitoringConfig):
        """
        Initialize conversation monitor
        
        Args:
            config: Monitoring configuration
        """
        self.config = config
        
        # Conversation tracking
        self.segments: Deque[SpeechSegment] = deque(maxlen=config.context_window_size)
        self.meeting_start_time = None
        self.current_speaker = None
        self.current_speaker_start = None
        
        # Participant tracking
        self.participant_stats = {}
        self.speaking_order = []
        
        # Context tracking
        self.recent_topics = deque(maxlen=5)
        self.conversation_context = ""
        
        logger.info("Conversation Monitor initialized")
    
    def start_monitoring(self, participants: List[str]):
        """
        Start monitoring a new conversation
        
        Args:
            participants: List of participant names
        """
        self.meeting_start_time = time.time()
        self.current_speaker = None
        self.current_speaker_start = None
        
        # Initialize participant stats
        self.participant_stats = {
            name: {
                'total_speaking_time': 0.0,
                'segment_count': 0,
                'average_segment_length': 0.0,
                'last_spoke': None,
                'topics_mentioned': []
            }
            for name in participants
        }
        
        self.segments.clear()
        self.speaking_order.clear()
        self.recent_topics.clear()
        self.conversation_context = ""
        
        logger.info(f"Started monitoring conversation with {len(participants)} participants")
    
    def add_speech(self, participant: str, text: str, timestamp: float) -> ConversationState:
        """
        Add a new speech segment and update conversation state
        
        Args:
            participant: Name of the speaking participant
            text: Transcribed speech text
            timestamp: Timestamp of the speech
            
        Returns:
            Updated conversation state
        """
        # Calculate segment duration (estimate based on text length)
        word_count = len(text.split())
        estimated_duration = word_count * 0.5  # ~0.5 seconds per word
        
        # Create speech segment
        segment = SpeechSegment(
            timestamp=timestamp,
            participant=participant,
            text=text,
            duration=estimated_duration,
            word_count=word_count
        )
        
        # Update speaker tracking
        self._update_speaker_tracking(participant, timestamp, estimated_duration)
        
        # Add segment to history
        self.segments.append(segment)
        
        # Update participant statistics
        self._update_participant_stats(participant, segment)
        
        # Update conversation context
        self._update_conversation_context(segment)
        
        # Extract topics if enabled
        if self.config.topic_extraction:
            self._extract_and_track_topics(text)
        
        # Build and return current state
        return self._build_conversation_state(timestamp)
    
    def _update_speaker_tracking(self, participant: str, timestamp: float, duration: float):
        """Update current speaker tracking"""
        
        # Check if speaker changed
        if self.current_speaker != participant:
            # New speaker
            self.current_speaker = participant
            self.current_speaker_start = timestamp
            
            # Add to speaking order if not already recent
            if not self.speaking_order or self.speaking_order[-1] != participant:
                self.speaking_order.append(participant)
        
        # Update participant's total speaking time
        if participant in self.participant_stats:
            self.participant_stats[participant]['total_speaking_time'] += duration
            self.participant_stats[participant]['last_spoke'] = timestamp
    
    def _update_participant_stats(self, participant: str, segment: SpeechSegment):
        """Update detailed participant statistics"""
        
        if participant not in self.participant_stats:
            self.participant_stats[participant] = {
                'total_speaking_time': 0.0,
                'segment_count': 0,
                'average_segment_length': 0.0,
                'last_spoke': None,
                'topics_mentioned': []
            }
        
        stats = self.participant_stats[participant]
        stats['segment_count'] += 1
        
        # Update average segment length
        total_segments = stats['segment_count']
        current_avg = stats['average_segment_length']
        stats['average_segment_length'] = (
            (current_avg * (total_segments - 1) + segment.word_count) / total_segments
        )
    
    def _update_conversation_context(self, segment: SpeechSegment):
        """Update conversation context string"""
        
        # Add new segment to context
        context_addition = f"{segment.participant}: {segment.text}\n"
        self.conversation_context += context_addition
        
        # Trim context if too long
        if len(self.conversation_context) > self.config.max_context_length:
            # Keep only the most recent part
            lines = self.conversation_context.split('\n')
            while len('\n'.join(lines)) > self.config.max_context_length and lines:
                lines.pop(0)
            self.conversation_context = '\n'.join(lines)
    
    def _extract_and_track_topics(self, text: str):
        """Extract and track topics from speech"""
        
        # Simple topic extraction based on keywords
        # In a production system, this could use more sophisticated NLP
        
        topic_keywords = [
            'project', 'feature', 'bug', 'issue', 'task', 'sprint',
            'deployment', 'testing', 'review', 'meeting', 'client',
            'database', 'api', 'frontend', 'backend', 'mobile'
        ]
        
        text_lower = text.lower()
        found_topics = []
        
        for keyword in topic_keywords:
            if keyword in text_lower:
                found_topics.append(keyword)
        
        # Add unique topics to recent topics
        for topic in found_topics:
            if topic not in self.recent_topics:
                self.recent_topics.append(topic)
    
    def _build_conversation_state(self, current_time: float) -> ConversationState:
        """Build current conversation state"""
        
        # Calculate current speaker duration
        current_speaker_duration = 0.0
        if self.current_speaker and self.current_speaker_start:
            current_speaker_duration = current_time - self.current_speaker_start
        
        # Calculate total meeting duration
        total_duration = 0.0
        if self.meeting_start_time:
            total_duration = current_time - self.meeting_start_time
        
        # Get participants who have spoken
        participants_spoken = list(self.speaking_order)
        
        # Build speaking pattern
        speaking_pattern = {
            name: stats['total_speaking_time']
            for name, stats in self.participant_stats.items()
        }
        
        return ConversationState(
            current_speaker=self.current_speaker,
            current_speaker_start_time=self.current_speaker_start or current_time,
            current_speaker_duration=current_speaker_duration,
            total_duration=total_duration,
            total_segments=len(self.segments),
            participants_spoken=participants_spoken,
            context=self.conversation_context,
            recent_topics=list(self.recent_topics),
            speaking_pattern=speaking_pattern
        )
    
    def get_participant_engagement(self) -> Dict[str, Dict]:
        """Get participant engagement metrics"""
        
        engagement_metrics = {}
        
        for participant, stats in self.participant_stats.items():
            # Calculate engagement score (0-1)
            speaking_time = stats['total_speaking_time']
            segment_count = stats['segment_count']
            avg_segment_length = stats['average_segment_length']
            
            # Normalize metrics
            speaking_score = min(1.0, speaking_time / 120.0)  # 2 minutes = full score
            participation_score = min(1.0, segment_count / 5.0)  # 5 segments = full score
            
            # Engagement score combines multiple factors
            engagement_score = (speaking_score * 0.4 + participation_score * 0.6)
            
            engagement_metrics[participant] = {
                'engagement_score': engagement_score,
                'speaking_time': speaking_time,
                'segment_count': segment_count,
                'average_segment_length': avg_segment_length,
                'last_spoke': stats['last_spoke'],
                'topics_mentioned': len(stats['topics_mentioned'])
            }
        
        return engagement_metrics
    
    def get_conversation_flow_analysis(self) -> Dict:
        """Analyze conversation flow patterns"""
        
        if len(self.segments) < 2:
            return {
                'speaker_transitions': 0,
                'average_turn_length': 0.0,
                'dominant_speaker': None,
                'quiet_participants': [],
                'conversation_balance': 0.0
            }
        
        # Count speaker transitions
        transitions = 0
        for i in range(1, len(self.segments)):
            if self.segments[i].participant != self.segments[i-1].participant:
                transitions += 1
        
        # Calculate average turn length
        total_duration = sum(segment.duration for segment in self.segments)
        avg_turn_length = total_duration / len(self.segments) if self.segments else 0.0
        
        # Find dominant speaker
        speaking_times = {}
        for participant, stats in self.participant_stats.items():
            speaking_times[participant] = stats['total_speaking_time']
        
        dominant_speaker = max(speaking_times.items(), key=lambda x: x[1])[0] if speaking_times else None
        
        # Find quiet participants (less than 30 seconds total)
        quiet_participants = [
            participant for participant, time in speaking_times.items()
            if time < 30.0
        ]
        
        # Calculate conversation balance (0-1, higher = more balanced)
        if speaking_times:
            times = list(speaking_times.values())
            max_time = max(times)
            min_time = min(times)
            balance = 1.0 - (max_time - min_time) / max(max_time, 1.0) if max_time > 0 else 1.0
        else:
            balance = 1.0
        
        return {
            'speaker_transitions': transitions,
            'average_turn_length': avg_turn_length,
            'dominant_speaker': dominant_speaker,
            'quiet_participants': quiet_participants,
            'conversation_balance': balance,
            'total_segments': len(self.segments),
            'unique_speakers': len(set(segment.participant for segment in self.segments))
        }
    
    def reset_monitoring(self):
        """Reset monitoring state for new meeting"""
        self.segments.clear()
        self.meeting_start_time = None
        self.current_speaker = None
        self.current_speaker_start = None
        self.participant_stats = {}
        self.speaking_order = []
        self.recent_topics.clear()
        self.conversation_context = ""
        
        logger.info("Conversation Monitor state reset")
