"""
Context-Aware Suggestion Engine

Generates intelligent, contextual suggestions for meeting moderation
based on conversation analysis, participant behavior, and meeting dynamics.
"""

import logging
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum

import google.generativeai as genai

from .topic_classifier import TopicClassification, IntentAnalysis
from .conversation_monitor import ConversationState

logger = logging.getLogger(__name__)


class SuggestionType(Enum):
    """Types of suggestions the engine can make"""
    REDIRECT_TOPIC = "redirect_topic"
    TIME_MANAGEMENT = "time_management"
    ENCOURAGE_PARTICIPATION = "encourage_participation"
    CLARIFY_BLOCKER = "clarify_blocker"
    SCHEDULE_FOLLOWUP = "schedule_followup"
    IMPROVE_FLOW = "improve_flow"
    WRAP_UP = "wrap_up"
    NEXT_PARTICIPANT = "next_participant"


@dataclass
class Suggestion:
    """A contextual suggestion for meeting moderation"""
    suggestion_type: SuggestionType
    message: str
    confidence: float
    priority: int  # 1-5, higher = more important
    timing: str  # 'immediate', 'after_current_speaker', 'end_of_phase'
    context: Dict[str, Any]
    reasoning: str
    
    # Effectiveness tracking
    suggested_at: float = field(default_factory=time.time)
    implemented: bool = False
    effectiveness_score: Optional[float] = None


@dataclass
class SuggestionConfig:
    """Configuration for suggestion engine"""
    confidence_threshold: float = 0.6
    max_suggestions_per_minute: int = 3
    enable_ai_suggestions: bool = True
    gemini_model: str = "gemini-2.5-flash"
    suggestion_cooldown: float = 30.0  # seconds between similar suggestions
    priority_threshold: int = 3  # minimum priority to suggest


class SuggestionEngine:
    """
    Context-Aware Suggestion Engine
    
    Analyzes meeting context and generates intelligent suggestions
    for improving meeting flow and effectiveness.
    """
    
    def __init__(self, gemini_api_key: str, config: SuggestionConfig):
        """
        Initialize suggestion engine
        
        Args:
            gemini_api_key: API key for Gemini AI
            config: Suggestion engine configuration
        """
        self.config = config
        self.gemini_api_key = gemini_api_key
        
        # Initialize Gemini if AI suggestions are enabled
        if config.enable_ai_suggestions:
            genai.configure(api_key=gemini_api_key)
            self.model = genai.GenerativeModel(config.gemini_model)
        else:
            self.model = None
        
        # Suggestion tracking
        self.suggestion_history = []
        self.recent_suggestions = {}  # type -> timestamp
        self.participant_patterns = {}  # participant -> behavior patterns
        
        # Template library
        self.suggestion_templates = self._initialize_templates()
        
        logger.info("Suggestion Engine initialized")
    
    def generate_suggestions(self, 
                           classification: TopicClassification,
                           conversation_state: ConversationState,
                           meeting_phase: str,
                           current_participant: str) -> List[Suggestion]:
        """
        Generate contextual suggestions based on current meeting state
        
        Args:
            classification: Topic classification result
            conversation_state: Current conversation state
            meeting_phase: Current meeting phase
            current_participant: Currently speaking participant
            
        Returns:
            List of prioritized suggestions
        """
        suggestions = []
        current_time = time.time()
        
        # Check rate limiting
        if self._is_rate_limited(current_time):
            return suggestions
        
        # Generate different types of suggestions
        suggestions.extend(self._generate_topic_suggestions(
            classification, conversation_state, meeting_phase, current_participant
        ))
        
        suggestions.extend(self._generate_flow_suggestions(
            conversation_state, meeting_phase, current_time
        ))
        
        suggestions.extend(self._generate_participation_suggestions(
            conversation_state, current_participant
        ))
        
        suggestions.extend(self._generate_time_management_suggestions(
            conversation_state, current_time
        ))
        
        # AI-powered contextual suggestions
        if self.config.enable_ai_suggestions and self.model:
            ai_suggestions = self._generate_ai_suggestions(
                classification, conversation_state, meeting_phase, current_participant
            )
            suggestions.extend(ai_suggestions)
        
        # Filter and prioritize suggestions
        filtered_suggestions = self._filter_and_prioritize(suggestions, current_time)
        
        # Track suggestions
        for suggestion in filtered_suggestions:
            self.suggestion_history.append(suggestion)
            self.recent_suggestions[suggestion.suggestion_type] = current_time
        
        return filtered_suggestions
    
    def _generate_topic_suggestions(self, 
                                  classification: TopicClassification,
                                  conversation_state: ConversationState,
                                  meeting_phase: str,
                                  current_participant: str) -> List[Suggestion]:
        """Generate suggestions based on topic analysis"""
        suggestions = []
        
        # Off-topic redirect suggestions
        if not classification.is_standup_relevant and classification.confidence > 0.7:
            if classification.topic_type == 'technical_deep_dive':
                suggestions.append(Suggestion(
                    suggestion_type=SuggestionType.REDIRECT_TOPIC,
                    message=f"This technical discussion might be better suited for a separate meeting. Could we note this as a follow-up item?",
                    confidence=classification.confidence,
                    priority=4,
                    timing='immediate',
                    context={'participant': current_participant, 'topic_type': classification.topic_type},
                    reasoning="Technical deep-dive detected during standup"
                ))
            elif classification.topic_type == 'off_topic_discussion':
                suggestions.append(Suggestion(
                    suggestion_type=SuggestionType.REDIRECT_TOPIC,
                    message=f"Let's keep our focus on standup updates. We can chat about this after the meeting.",
                    confidence=classification.confidence,
                    priority=3,
                    timing='immediate',
                    context={'participant': current_participant, 'topic_type': classification.topic_type},
                    reasoning="Off-topic discussion detected"
                ))
        
        # Blocker clarification suggestions
        if (classification.intent_analysis and 
            classification.intent_analysis.primary_intent == 'blocker' and
            classification.intent_analysis.confidence > 0.6):
            
            suggestions.append(Suggestion(
                suggestion_type=SuggestionType.CLARIFY_BLOCKER,
                message=f"Can you clarify what specific help you need with this blocker? Should we schedule time to address it?",
                confidence=classification.intent_analysis.confidence,
                priority=4,
                timing='after_current_speaker',
                context={'participant': current_participant, 'blocker_mentioned': True},
                reasoning="Blocker mentioned but may need clarification"
            ))
        
        return suggestions
    
    def _generate_flow_suggestions(self,
                                 conversation_state: ConversationState,
                                 meeting_phase: str,
                                 current_time: float) -> List[Suggestion]:
        """Generate suggestions for improving conversation flow"""
        suggestions = []
        
        # Phase transition suggestions
        if meeting_phase == 'individual_updates':
            participants_updated = len(conversation_state.participants_spoken)
            total_participants = len(conversation_state.speaking_pattern)
            
            if participants_updated >= total_participants * 0.8:  # 80% have spoken
                suggestions.append(Suggestion(
                    suggestion_type=SuggestionType.WRAP_UP,
                    message="Most team members have shared their updates. Let's wrap up with any final blockers or questions.",
                    confidence=0.8,
                    priority=3,
                    timing='end_of_phase',
                    context={'participants_updated': participants_updated, 'total': total_participants},
                    reasoning="Most participants have given updates"
                ))
        
        # Flow improvement based on conversation balance
        flow_analysis = self._analyze_conversation_flow(conversation_state)
        if flow_analysis['needs_rebalancing']:
            suggestions.append(Suggestion(
                suggestion_type=SuggestionType.IMPROVE_FLOW,
                message=flow_analysis['suggestion_message'],
                confidence=0.7,
                priority=2,
                timing='after_current_speaker',
                context=flow_analysis,
                reasoning="Conversation flow needs rebalancing"
            ))
        
        return suggestions
    
    def _generate_participation_suggestions(self,
                                          conversation_state: ConversationState,
                                          current_participant: str) -> List[Suggestion]:
        """Generate suggestions for encouraging participation"""
        suggestions = []
        
        # Find quiet participants
        speaking_times = conversation_state.speaking_pattern
        if speaking_times:
            avg_speaking_time = sum(speaking_times.values()) / len(speaking_times)
            quiet_participants = [
                name for name, time in speaking_times.items()
                if time < avg_speaking_time * 0.3 and name != current_participant
            ]
            
            if quiet_participants and len(quiet_participants) <= 2:
                participant_list = ", ".join(quiet_participants)
                suggestions.append(Suggestion(
                    suggestion_type=SuggestionType.ENCOURAGE_PARTICIPATION,
                    message=f"We haven't heard from {participant_list} yet. Would you like to share your update?",
                    confidence=0.8,
                    priority=2,
                    timing='after_current_speaker',
                    context={'quiet_participants': quiet_participants},
                    reasoning="Some participants haven't contributed much"
                ))
        
        return suggestions
    
    def _generate_time_management_suggestions(self,
                                            conversation_state: ConversationState,
                                            current_time: float) -> List[Suggestion]:
        """Generate time management suggestions"""
        suggestions = []
        
        meeting_duration = conversation_state.total_duration / 60.0  # minutes
        
        # Long meeting warning
        if meeting_duration > 20:  # 20 minutes
            suggestions.append(Suggestion(
                suggestion_type=SuggestionType.TIME_MANAGEMENT,
                message=f"We've been going for {meeting_duration:.1f} minutes. Let's try to wrap up soon.",
                confidence=0.9,
                priority=4,
                timing='immediate',
                context={'duration_minutes': meeting_duration},
                reasoning="Meeting running long"
            ))
        elif meeting_duration > 15:  # 15 minutes
            suggestions.append(Suggestion(
                suggestion_type=SuggestionType.TIME_MANAGEMENT,
                message="We're approaching our time limit. Let's focus on key updates and blockers.",
                confidence=0.7,
                priority=3,
                timing='after_current_speaker',
                context={'duration_minutes': meeting_duration},
                reasoning="Meeting approaching time limit"
            ))
        
        return suggestions
    
    def _generate_ai_suggestions(self,
                               classification: TopicClassification,
                               conversation_state: ConversationState,
                               meeting_phase: str,
                               current_participant: str) -> List[Suggestion]:
        """Generate AI-powered contextual suggestions"""
        
        try:
            # Build context for AI
            context_summary = self._build_ai_context(
                classification, conversation_state, meeting_phase, current_participant
            )
            
            prompt = f"""As an AI meeting moderator, analyze this standup meeting context and suggest improvements:

{context_summary}

Generate 1-2 specific, actionable suggestions to improve this meeting. Consider:
- Meeting flow and efficiency
- Participant engagement
- Topic relevance
- Time management
- Communication clarity

Respond in JSON format:
[
    {{
        "type": "redirect_topic|time_management|encourage_participation|clarify_blocker|schedule_followup|improve_flow|wrap_up|next_participant",
        "message": "Specific suggestion message",
        "confidence": 0.0-1.0,
        "priority": 1-5,
        "timing": "immediate|after_current_speaker|end_of_phase",
        "reasoning": "Why this suggestion is needed"
    }}
]

Only suggest if there's a clear improvement opportunity. Return empty array if no suggestions needed."""

            response = self.model.generate_content(prompt)
            
            # Parse AI suggestions
            import json
            try:
                ai_suggestions_data = json.loads(response.text.strip())
                ai_suggestions = []
                
                for suggestion_data in ai_suggestions_data:
                    suggestion = Suggestion(
                        suggestion_type=SuggestionType(suggestion_data.get('type', 'improve_flow')),
                        message=suggestion_data.get('message', ''),
                        confidence=float(suggestion_data.get('confidence', 0.5)),
                        priority=int(suggestion_data.get('priority', 3)),
                        timing=suggestion_data.get('timing', 'after_current_speaker'),
                        context={'ai_generated': True, 'participant': current_participant},
                        reasoning=suggestion_data.get('reasoning', 'AI-generated suggestion')
                    )
                    ai_suggestions.append(suggestion)
                
                return ai_suggestions
                
            except (json.JSONDecodeError, KeyError, ValueError) as e:
                logger.warning(f"Failed to parse AI suggestions: {e}")
                return []
                
        except Exception as e:
            logger.warning(f"AI suggestion generation failed: {e}")
            return []
    
    def _build_ai_context(self,
                         classification: TopicClassification,
                         conversation_state: ConversationState,
                         meeting_phase: str,
                         current_participant: str) -> str:
        """Build context summary for AI analysis"""
        
        context_parts = [
            f"Meeting Phase: {meeting_phase}",
            f"Current Speaker: {current_participant}",
            f"Meeting Duration: {conversation_state.total_duration / 60:.1f} minutes",
            f"Total Segments: {conversation_state.total_segments}",
            f"Participants Spoken: {len(conversation_state.participants_spoken)}"
        ]
        
        # Add topic classification info
        if classification:
            context_parts.extend([
                f"Current Topic Type: {classification.topic_type}",
                f"Standup Relevant: {classification.is_standup_relevant}",
                f"Classification Confidence: {classification.confidence:.2f}"
            ])
            
            if classification.intent_analysis:
                intent = classification.intent_analysis
                context_parts.extend([
                    f"Speaker Intent: {intent.primary_intent}",
                    f"Sentiment: {intent.sentiment}",
                    f"Urgency Level: {intent.urgency_level:.2f}"
                ])
        
        # Add conversation flow info
        if conversation_state.speaking_pattern:
            speaking_times = list(conversation_state.speaking_pattern.values())
            avg_time = sum(speaking_times) / len(speaking_times)
            max_time = max(speaking_times)
            context_parts.extend([
                f"Average Speaking Time: {avg_time:.1f}s",
                f"Longest Speaker Time: {max_time:.1f}s"
            ])
        
        return "\n".join(context_parts)
    
    def _analyze_conversation_flow(self, conversation_state: ConversationState) -> Dict[str, Any]:
        """Analyze conversation flow and suggest improvements"""
        
        analysis = {
            'needs_rebalancing': False,
            'suggestion_message': '',
            'dominant_speaker': None,
            'quiet_participants': []
        }
        
        if not conversation_state.speaking_pattern:
            return analysis
        
        speaking_times = conversation_state.speaking_pattern
        total_speakers = len(speaking_times)
        
        if total_speakers < 2:
            return analysis
        
        # Find dominant speaker
        max_time = max(speaking_times.values())
        avg_time = sum(speaking_times.values()) / total_speakers
        
        for participant, time in speaking_times.items():
            if time == max_time and time > avg_time * 2:
                analysis['dominant_speaker'] = participant
                analysis['needs_rebalancing'] = True
                analysis['suggestion_message'] = f"Let's make sure everyone gets a chance to share their updates."
                break
        
        return analysis
    
    def _filter_and_prioritize(self, suggestions: List[Suggestion], current_time: float) -> List[Suggestion]:
        """Filter and prioritize suggestions"""
        
        # Remove duplicates and low-confidence suggestions
        filtered = []
        seen_types = set()
        
        for suggestion in suggestions:
            # Skip if confidence too low
            if suggestion.confidence < self.config.confidence_threshold:
                continue
            
            # Skip if priority too low
            if suggestion.priority < self.config.priority_threshold:
                continue
            
            # Skip if similar suggestion was made recently
            if self._is_suggestion_on_cooldown(suggestion.suggestion_type, current_time):
                continue
            
            # Skip duplicate types (keep highest priority)
            if suggestion.suggestion_type in seen_types:
                continue
            
            seen_types.add(suggestion.suggestion_type)
            filtered.append(suggestion)
        
        # Sort by priority (descending) then confidence (descending)
        filtered.sort(key=lambda s: (s.priority, s.confidence), reverse=True)
        
        # Limit number of suggestions
        return filtered[:3]  # Max 3 suggestions at once
    
    def _is_rate_limited(self, current_time: float) -> bool:
        """Check if we're rate limited for suggestions"""
        
        recent_suggestions = [
            s for s in self.suggestion_history
            if current_time - s.suggested_at < 60.0  # Last minute
        ]
        
        return len(recent_suggestions) >= self.config.max_suggestions_per_minute
    
    def _is_suggestion_on_cooldown(self, suggestion_type: SuggestionType, current_time: float) -> bool:
        """Check if suggestion type is on cooldown"""
        
        if suggestion_type not in self.recent_suggestions:
            return False
        
        last_suggested = self.recent_suggestions[suggestion_type]
        return current_time - last_suggested < self.config.suggestion_cooldown
    
    def _initialize_templates(self) -> Dict[SuggestionType, List[str]]:
        """Initialize suggestion message templates"""
        
        return {
            SuggestionType.REDIRECT_TOPIC: [
                "This sounds like a great topic for a separate discussion. Should we park it for now?",
                "Let's keep our standup focused and schedule time to dive deeper into this.",
                "This might be better addressed in a dedicated meeting. Can we note it as a follow-up?"
            ],
            SuggestionType.TIME_MANAGEMENT: [
                "We're running a bit long. Let's try to keep updates brief.",
                "Time check! Let's focus on the key points.",
                "We're approaching our time limit. Let's wrap up efficiently."
            ],
            SuggestionType.ENCOURAGE_PARTICIPATION: [
                "We haven't heard from everyone yet. {participant}, would you like to share?",
                "Let's make sure everyone gets a chance to update the team.",
                "{participant}, any updates or blockers to share?"
            ],
            SuggestionType.CLARIFY_BLOCKER: [
                "Can you clarify what specific help you need with this blocker?",
                "Should we schedule time to address this blocker after the standup?",
                "What would unblock you on this issue?"
            ],
            SuggestionType.SCHEDULE_FOLLOWUP: [
                "This sounds like it needs a follow-up meeting. Should we schedule one?",
                "Let's add this to our parking lot for detailed discussion later.",
                "This deserves more time than we have in standup. Follow-up meeting?"
            ]
        }
    
    def mark_suggestion_implemented(self, suggestion: Suggestion, effectiveness_score: float):
        """Mark a suggestion as implemented and track its effectiveness"""
        suggestion.implemented = True
        suggestion.effectiveness_score = effectiveness_score
        
        logger.info(f"Suggestion implemented with effectiveness score: {effectiveness_score}")
    
    def get_suggestion_analytics(self) -> Dict[str, Any]:
        """Get analytics on suggestion effectiveness"""
        
        if not self.suggestion_history:
            return {
                'total_suggestions': 0,
                'implemented_suggestions': 0,
                'average_effectiveness': 0.0,
                'most_common_type': None,
                'success_rate_by_type': {}
            }
        
        implemented = [s for s in self.suggestion_history if s.implemented]
        
        # Calculate effectiveness
        effectiveness_scores = [s.effectiveness_score for s in implemented if s.effectiveness_score is not None]
        avg_effectiveness = sum(effectiveness_scores) / len(effectiveness_scores) if effectiveness_scores else 0.0
        
        # Most common suggestion type
        type_counts = {}
        for suggestion in self.suggestion_history:
            type_counts[suggestion.suggestion_type] = type_counts.get(suggestion.suggestion_type, 0) + 1
        
        most_common_type = max(type_counts.items(), key=lambda x: x[1])[0] if type_counts else None
        
        # Success rate by type
        success_rate_by_type = {}
        for suggestion_type in SuggestionType:
            type_suggestions = [s for s in self.suggestion_history if s.suggestion_type == suggestion_type]
            type_implemented = [s for s in type_suggestions if s.implemented]
            
            if type_suggestions:
                success_rate_by_type[suggestion_type.value] = len(type_implemented) / len(type_suggestions)
        
        return {
            'total_suggestions': len(self.suggestion_history),
            'implemented_suggestions': len(implemented),
            'average_effectiveness': avg_effectiveness,
            'most_common_type': most_common_type.value if most_common_type else None,
            'success_rate_by_type': success_rate_by_type
        }
