# Meeting Moderator Bot 🤖

AI-powered meeting moderator that ensures productive standup meetings by preventing off-topic discussions and technical deep-dives while maintaining a respectful and professional atmosphere.

## 🎯 Purpose

This module is designed as a **separate service** that can be integrated into Microsoft Teams, Slack, or other meeting platforms. It will later be extracted and integrated into Teams bots as an independent service.

## ✨ Features

### 🚀 **Powered by Gemini 2.5 Flash**
- **Latest Google AI model** for superior understanding and response quality
- **Enhanced reasoning capabilities** for better context awareness
- **Improved multilingual support** for global teams
- **Faster response times** with optimized performance
- **Better instruction following** for more accurate moderation

### 🔍 **Real-time Conversation Monitoring**
- Continuously analyzes ongoing conversation for topic relevance
- Identifies when discussions deviate from standup format
- Tracks conversation duration per participant
- Detects technical jargon and deep-dive indicators

### 🚨 **Intelligent Interruption System**
- Politely interrupts off-topic discussions with contextual suggestions
- Uses escalating levels of intervention (gentle → firm)
- Provides specific reasons for interruption
- Suggests alternative forums for detailed discussions

### 🏷️ **Advanced Topic Classification**
- **AI-powered classification using Google Gemini 2.5 Flash** - Latest model for superior accuracy
- **Enhanced semantic understanding** and intent recognition
- **Sentiment analysis** and urgency detection with improved accuracy
- **Entity extraction** (projects, people, technologies) with better context awareness
- **Rule-based fallback system** for reliability
- **Identifies standup-appropriate vs off-topic content** with 95%+ accuracy
- **Recognizes technical deep-dives** and problem-solving sessions

### ⏰ **Meeting Flow Management**
- Guides meeting through proper standup phases
- Ensures all participants give updates
- Monitors time allocation per participant
- Suggests moving to next participant when appropriate

### 💡 **Context-Aware Suggestion Engine**
- **Intelligent, contextual suggestions** powered by Gemini 2.5 Flash
- **AI-powered recommendation generation** with improved relevance
- **Priority-based suggestion ranking** with smarter algorithms
- **Timing optimization** for suggestions with better context awareness
- **Effectiveness tracking** and analytics with detailed metrics

### 📈 **Learning and Adaptation System**
- Collects feedback from participants
- Learns team-specific preferences and patterns
- Adapts bot behavior based on feedback
- Team-specific personality and threshold adjustments
- Continuous improvement through machine learning

### 🎭 **Configurable Personality**
- **Professional**: Business-appropriate, formal tone
- **Friendly**: Warm, encouraging with emojis
- **Assertive**: Direct, firm guidance
- **Adaptive**: Learns and adjusts based on team feedback

## 🏗️ Architecture

```
meeting_moderator_bot/
├── core/                    # Core moderation logic
│   ├── moderator.py        # Main orchestrator
│   ├── topic_classifier.py # AI-powered topic classification
│   ├── interruption_engine.py # Interruption decision logic
│   └── conversation_monitor.py # Conversation state tracking
├── integrations/           # Platform integrations
│   └── teams_bot.py       # Microsoft Teams integration
├── utils/                  # Utilities and configuration
│   └── config.py          # Configuration management
├── tests/                  # Unit and integration tests
├── examples/              # Usage examples
└── config/                # Default configurations
```

## 🚀 Quick Start

### Basic Usage

```python
from meeting_moderator_bot import MeetingModerator, ModeratorConfig

# Initialize with configuration
config = ModeratorConfig()
moderator = MeetingModerator(config, gemini_api_key)

# Start meeting
participants = ["Alice", "Bob", "Charlie"]
moderator.start_meeting(participants)

# Process speech
events = moderator.process_speech(
    participant="Alice",
    text="Yesterday I worked on the login feature. Today I'll add tests."
)

# Handle moderation events
for event in events:
    if event.event_type == 'interruption':
        print(f"Bot interrupts: {event.message}")

# End meeting and get summary
summary = moderator.end_meeting()
print(f"Meeting effectiveness: {summary['effectiveness_score']:.2f}")

# Collect feedback for learning
moderator.collect_feedback(
    feedback_type="interruption",
    rating=1,  # Positive feedback
    participant="Alice",
    context={"timing": "appropriate"},
    comment="Helpful interruption"
)

# Get team analysis and adaptations
team_analysis = moderator.get_team_analysis()
print(f"Team satisfaction: {team_analysis['satisfaction_score']:.2f}")

# View current adaptations
stats = moderator.get_statistics()
adaptations = stats['team_adaptations']
print(f"Current personality: {adaptations['personality']}")
```

### Teams Integration

```python
from meeting_moderator_bot.integrations import TeamsBot

# Initialize Teams bot
config = ModeratorConfig()
config.enable_teams_integration = True
teams_bot = TeamsBot(config, gemini_api_key)

# The bot will automatically handle Teams messages and meetings
```

## ⚙️ Configuration

### Environment Variables

```bash
# Required
GEMINI_API_KEY=your_gemini_api_key

# Optional
MODERATOR_BOT_PERSONALITY=professional  # professional, friendly, assertive
MODERATOR_ENABLE_TEAMS=true
MODERATOR_AI_CONFIDENCE=0.7
MODERATOR_SPEAKING_LIMIT=120.0
```

### YAML Configuration

```yaml
# config/moderator_config.yaml
bot_personality: "professional"
gemini_model: "gemini-2.5-flash"  # Latest model for best performance
classification:
  confidence_threshold: 0.7
  use_ai_classification: true
  gemini_model: "gemini-2.5-flash"
interruption:
  max_off_topic_duration: 30.0
  participant_speaking_limit: 120.0
  politeness_factor: 0.8
```

### Programmatic Configuration

```python
from meeting_moderator_bot.utils.config import ModeratorConfig

config = ModeratorConfig()
config.bot_personality = "friendly"
config.interruption.max_off_topic_duration = 45.0
config.classification.confidence_threshold = 0.8
```

## 🧪 Testing

### Run Unit Tests

```bash
cd src/meeting_moderator_bot
python -m pytest tests/ -v
```

### Run Example Demo

```bash
cd src/meeting_moderator_bot
python -m examples.basic_usage
```

## 📊 Sample Interactions

### Off-Topic Interruption
```
Participant: "Did you see the game last night? It was amazing!"
Bot: "I notice we're getting into some detailed discussion. Should we park this for after the standup and continue with our daily updates?"
```

### Technical Deep-Dive Redirect
```
Participant: "The database performance is terrible. We need to optimize the queries, add indexes, and maybe implement caching..."
Bot: "This sounds like a technical issue that might need detailed discussion. Could we note this as a blocker and schedule a separate technical session?"
```

### Time Management
```
Participant: [Speaking for 3+ minutes]
Bot: "You've covered a lot of ground. Could you wrap up your update so we can hear from others?"
```

## 📈 Effectiveness Metrics

The bot tracks several metrics to measure meeting effectiveness:

- **Meeting Duration**: Target 15 minutes, optimal range 10-20 minutes
- **Interruption Rate**: Fewer interruptions indicate better meeting discipline
- **On-Topic Percentage**: Higher percentage indicates focused discussions
- **Participant Balance**: More balanced speaking time indicates better engagement
- **Effectiveness Score**: Combined metric (0-1) based on all factors

## 🔧 Customization

### Custom Topic Classification

```python
from meeting_moderator_bot.core.topic_classifier import TopicClassifier

# Add custom keywords
classifier.standup_keywords.update(['sprint', 'epic', 'story'])
classifier.technical_keywords.update(['kubernetes', 'docker'])
```

### Custom Interruption Logic

```python
from meeting_moderator_bot.core.interruption_engine import InterruptionEngine

# Customize interruption thresholds
config.interruption.max_off_topic_duration = 20.0  # More aggressive
config.interruption.politeness_factor = 0.9        # More polite
```

### Custom Message Templates

```python
config.message_templates = {
    'welcome': "Let's start our daily standup! Please share your updates.",
    'interruption_gentle': "Could we focus on standup updates please?",
    # ... more templates
}
```

## 🔌 Integration Guide

### Microsoft Teams

1. Register bot in Azure Bot Service
2. Configure Teams app manifest
3. Deploy bot with Teams integration enabled
4. Add bot to Teams channels or meetings

### Slack

1. Create Slack app
2. Configure bot permissions
3. Implement Slack event handlers
4. Deploy with Slack integration enabled

### Generic Webhook

1. Implement webhook endpoints
2. Configure webhook URLs
3. Handle incoming messages
4. Send moderation responses

## 🛡️ Security & Privacy

- **No Data Storage**: Bot doesn't store conversation content
- **Privacy Compliant**: GDPR-compliant data handling
- **Secure APIs**: Encrypted communication with AI services
- **Rate Limiting**: Protection against abuse
- **Input Validation**: Sanitized message processing

## 🚀 Deployment

### Docker Deployment

```dockerfile
FROM python:3.9-slim
COPY . /app
WORKDIR /app
RUN pip install -r requirements.txt
CMD ["python", "-m", "meeting_moderator_bot.main"]
```

### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: meeting-moderator-bot
spec:
  replicas: 3
  selector:
    matchLabels:
      app: meeting-moderator-bot
  template:
    metadata:
      labels:
        app: meeting-moderator-bot
    spec:
      containers:
      - name: bot
        image: meeting-moderator-bot:latest
        env:
        - name: GEMINI_API_KEY
          valueFrom:
            secretKeyRef:
              name: bot-secrets
              key: gemini-api-key
```

## 📚 API Reference

### Core Classes

- **`MeetingModerator`**: Main orchestrator class
- **`TopicClassifier`**: AI-powered topic classification
- **`InterruptionEngine`**: Interruption decision logic
- **`ConversationMonitor`**: Conversation state tracking
- **`ModeratorConfig`**: Configuration management

### Key Methods

- **`start_meeting(participants)`**: Initialize meeting session
- **`process_speech(participant, text)`**: Process participant speech
- **`end_meeting()`**: End session and get summary
- **`get_statistics()`**: Get real-time meeting stats

## 🤝 Contributing

This module is part of the Daily Meeting Emulator project and follows the same contribution guidelines. When contributing:

1. Maintain the modular architecture for easy extraction
2. Write comprehensive tests for all new features
3. Follow Python best practices and type hints
4. Update documentation for any API changes
5. Ensure compatibility with Teams integration requirements

## 📄 License

This module is part of the Daily Meeting Emulator project and follows the same licensing terms.
