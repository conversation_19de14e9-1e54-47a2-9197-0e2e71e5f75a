"""
Integration Test Runner

Comprehensive test runner for all Meeting Moderator Bot components.
Runs integration tests, performance tests, and user testing scenarios.
"""

import sys
import time
import asyncio
import unittest
from typing import Dict, Any, List

# Import test modules
from .test_integration import (
    TestBaseIntegration, TestWebhookIntegration, 
    TestEndToEndIntegration, TestPerformanceAndScalability
)
from .test_learning_system import TestLearningSystem, TestFeedbackEntry, TestTeamProfile
from .test_performance import (
    TestPerformanceBenchmarks, TestConcurrencyAndScalability, TestResourceUsage
)
from .user_testing_scenarios import UserTestingFramework, run_user_testing_demo

from ..utils.config import ModeratorConfig


class IntegrationTestRunner:
    """
    Comprehensive test runner for Meeting Moderator Bot
    
    Runs all types of tests and generates detailed reports.
    """
    
    def __init__(self):
        """Initialize test runner"""
        self.test_results = {}
        self.config = ModeratorConfig()
        self.config.classification.use_ai_classification = False  # Use rule-based for consistent testing
        
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all test suites"""
        
        print("🧪 Meeting Moderator Bot - Comprehensive Test Suite")
        print("=" * 60)
        
        # Run unit tests
        print("\n📋 Running Unit Tests...")
        unit_results = self._run_unit_tests()
        
        # Run integration tests
        print("\n🔗 Running Integration Tests...")
        integration_results = self._run_integration_tests()
        
        # Run performance tests
        print("\n⚡ Running Performance Tests...")
        performance_results = self._run_performance_tests()
        
        # Run user testing scenarios
        print("\n👥 Running User Testing Scenarios...")
        user_testing_results = self._run_user_testing()
        
        # Compile overall results
        overall_results = {
            'unit_tests': unit_results,
            'integration_tests': integration_results,
            'performance_tests': performance_results,
            'user_testing': user_testing_results,
            'summary': self._generate_summary(
                unit_results, integration_results, 
                performance_results, user_testing_results
            )
        }
        
        # Display final report
        self._display_final_report(overall_results)
        
        return overall_results
    
    def _run_unit_tests(self) -> Dict[str, Any]:
        """Run unit tests"""
        
        test_suites = [
            TestLearningSystem,
            TestFeedbackEntry,
            TestTeamProfile
        ]
        
        results = {}
        total_tests = 0
        total_failures = 0
        
        for test_suite in test_suites:
            suite_name = test_suite.__name__
            print(f"  Running {suite_name}...")
            
            # Create test suite
            loader = unittest.TestLoader()
            suite = loader.loadTestsFromTestCase(test_suite)
            
            # Run tests
            runner = unittest.TextTestRunner(verbosity=0, stream=open('/dev/null', 'w'))
            result = runner.run(suite)
            
            # Collect results
            suite_results = {
                'tests_run': result.testsRun,
                'failures': len(result.failures),
                'errors': len(result.errors),
                'success_rate': (result.testsRun - len(result.failures) - len(result.errors)) / max(result.testsRun, 1)
            }
            
            results[suite_name] = suite_results
            total_tests += result.testsRun
            total_failures += len(result.failures) + len(result.errors)
            
            print(f"    ✅ {suite_results['tests_run']} tests, {suite_results['failures']} failures, {suite_results['errors']} errors")
        
        overall_success_rate = (total_tests - total_failures) / max(total_tests, 1)
        
        return {
            'suites': results,
            'total_tests': total_tests,
            'total_failures': total_failures,
            'overall_success_rate': overall_success_rate
        }
    
    def _run_integration_tests(self) -> Dict[str, Any]:
        """Run integration tests"""
        
        test_suites = [
            TestBaseIntegration,
            TestWebhookIntegration,
            TestEndToEndIntegration,
            TestPerformanceAndScalability
        ]
        
        results = {}
        total_tests = 0
        total_failures = 0
        
        for test_suite in test_suites:
            suite_name = test_suite.__name__
            print(f"  Running {suite_name}...")
            
            # Create test suite
            loader = unittest.TestLoader()
            suite = loader.loadTestsFromTestCase(test_suite)
            
            # Run tests
            runner = unittest.TextTestRunner(verbosity=0, stream=open('/dev/null', 'w'))
            result = runner.run(suite)
            
            # Collect results
            suite_results = {
                'tests_run': result.testsRun,
                'failures': len(result.failures),
                'errors': len(result.errors),
                'success_rate': (result.testsRun - len(result.failures) - len(result.errors)) / max(result.testsRun, 1)
            }
            
            results[suite_name] = suite_results
            total_tests += result.testsRun
            total_failures += len(result.failures) + len(result.errors)
            
            print(f"    ✅ {suite_results['tests_run']} tests, {suite_results['failures']} failures, {suite_results['errors']} errors")
        
        overall_success_rate = (total_tests - total_failures) / max(total_tests, 1)
        
        return {
            'suites': results,
            'total_tests': total_tests,
            'total_failures': total_failures,
            'overall_success_rate': overall_success_rate
        }
    
    def _run_performance_tests(self) -> Dict[str, Any]:
        """Run performance tests"""
        
        test_suites = [
            TestPerformanceBenchmarks,
            TestConcurrencyAndScalability,
            TestResourceUsage
        ]
        
        results = {}
        performance_metrics = {}
        
        for test_suite in test_suites:
            suite_name = test_suite.__name__
            print(f"  Running {suite_name}...")
            
            start_time = time.time()
            
            # Create test suite
            loader = unittest.TestLoader()
            suite = loader.loadTestsFromTestCase(test_suite)
            
            # Run tests
            runner = unittest.TextTestRunner(verbosity=0, stream=open('/dev/null', 'w'))
            result = runner.run(suite)
            
            execution_time = time.time() - start_time
            
            # Collect results
            suite_results = {
                'tests_run': result.testsRun,
                'failures': len(result.failures),
                'errors': len(result.errors),
                'execution_time': execution_time,
                'success_rate': (result.testsRun - len(result.failures) - len(result.errors)) / max(result.testsRun, 1)
            }
            
            results[suite_name] = suite_results
            
            print(f"    ⚡ {suite_results['tests_run']} tests in {execution_time:.2f}s, {suite_results['failures']} failures")
        
        return {
            'suites': results,
            'performance_metrics': performance_metrics
        }
    
    def _run_user_testing(self) -> Dict[str, Any]:
        """Run user testing scenarios"""
        
        try:
            # Run user testing demo
            results = run_user_testing_demo()
            
            print(f"  ✅ User testing completed")
            print(f"    Success rate: {results['success_rate']:.1%}")
            print(f"    Average effectiveness: {results['average_effectiveness']:.2f}")
            
            return results
            
        except Exception as e:
            print(f"  ❌ User testing failed: {e}")
            return {
                'success_rate': 0.0,
                'error': str(e)
            }
    
    def _generate_summary(self, unit_results: Dict, integration_results: Dict,
                         performance_results: Dict, user_testing_results: Dict) -> Dict[str, Any]:
        """Generate overall test summary"""
        
        # Calculate overall metrics
        total_tests = (
            unit_results.get('total_tests', 0) + 
            integration_results.get('total_tests', 0) +
            sum(suite.get('tests_run', 0) for suite in performance_results.get('suites', {}).values())
        )
        
        total_failures = (
            unit_results.get('total_failures', 0) + 
            integration_results.get('total_failures', 0) +
            sum(suite.get('failures', 0) + suite.get('errors', 0) for suite in performance_results.get('suites', {}).values())
        )
        
        overall_success_rate = (total_tests - total_failures) / max(total_tests, 1)
        
        # Determine overall status
        status = "PASS"
        if overall_success_rate < 0.8:
            status = "FAIL"
        elif overall_success_rate < 0.9:
            status = "WARNING"
        
        # Generate recommendations
        recommendations = []
        
        if unit_results.get('overall_success_rate', 1.0) < 0.9:
            recommendations.append("Fix failing unit tests")
        
        if integration_results.get('overall_success_rate', 1.0) < 0.9:
            recommendations.append("Address integration test failures")
        
        if user_testing_results.get('success_rate', 1.0) < 0.8:
            recommendations.append("Improve user testing scenario performance")
        
        if user_testing_results.get('average_effectiveness', 1.0) < 0.7:
            recommendations.append("Enhance meeting effectiveness algorithms")
        
        return {
            'overall_status': status,
            'total_tests': total_tests,
            'total_failures': total_failures,
            'overall_success_rate': overall_success_rate,
            'user_testing_success_rate': user_testing_results.get('success_rate', 0.0),
            'average_meeting_effectiveness': user_testing_results.get('average_effectiveness', 0.0),
            'recommendations': recommendations
        }
    
    def _display_final_report(self, results: Dict[str, Any]) -> None:
        """Display final test report"""
        
        summary = results['summary']
        
        print(f"\n🎯 Final Test Report")
        print("=" * 50)
        print(f"Overall Status: {summary['overall_status']}")
        print(f"Total Tests: {summary['total_tests']}")
        print(f"Total Failures: {summary['total_failures']}")
        print(f"Success Rate: {summary['overall_success_rate']:.1%}")
        print(f"User Testing Success: {summary['user_testing_success_rate']:.1%}")
        print(f"Meeting Effectiveness: {summary['average_meeting_effectiveness']:.2f}")
        
        if summary['recommendations']:
            print(f"\n💡 Recommendations:")
            for rec in summary['recommendations']:
                print(f"  • {rec}")
        
        # Status emoji
        if summary['overall_status'] == "PASS":
            print(f"\n✅ All tests passed! Meeting Moderator Bot is ready for deployment.")
        elif summary['overall_status'] == "WARNING":
            print(f"\n⚠️ Tests passed with warnings. Review recommendations before deployment.")
        else:
            print(f"\n❌ Tests failed. Address issues before deployment.")


def main():
    """Main test runner entry point"""
    
    runner = IntegrationTestRunner()
    results = runner.run_all_tests()
    
    # Exit with appropriate code
    if results['summary']['overall_status'] == "FAIL":
        sys.exit(1)
    elif results['summary']['overall_status'] == "WARNING":
        sys.exit(2)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()
