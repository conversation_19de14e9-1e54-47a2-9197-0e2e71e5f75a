"""
Base Integration Framework

Provides abstract base classes and interfaces for integrating the Meeting Moderator Bot
with various platforms (Teams, Slack, Zoom, etc.) through a plugin architecture.
"""

import logging
import asyncio
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum

from ..core.moderator import MeetingModerator, ModerationEvent
from ..utils.config import ModeratorConfig

logger = logging.getLogger(__name__)


class PlatformType(Enum):
    """Supported platform types"""
    TEAMS = "teams"
    SLACK = "slack"
    ZOOM = "zoom"
    WEBEX = "webex"
    WEBHOOK = "webhook"
    CUSTOM = "custom"


class MessageType(Enum):
    """Types of messages the integration can handle"""
    TEXT = "text"
    AUDIO = "audio"
    VIDEO = "video"
    FILE = "file"
    REACTION = "reaction"
    SYSTEM = "system"


@dataclass
class PlatformMessage:
    """Standardized message format across platforms"""
    message_id: str
    sender: str
    content: str
    timestamp: float
    message_type: MessageType
    platform_data: Dict[str, Any] = field(default_factory=dict)
    
    # Meeting context
    meeting_id: Optional[str] = None
    channel_id: Optional[str] = None
    thread_id: Optional[str] = None
    
    # Metadata
    is_bot_message: bool = False
    mentions: List[str] = field(default_factory=list)
    attachments: List[Dict[str, Any]] = field(default_factory=list)


@dataclass
class PlatformResponse:
    """Standardized response format for platforms"""
    message: str
    response_type: str  # 'text', 'card', 'notification', 'private'
    target: Optional[str] = None  # Specific user or channel
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Formatting options
    is_urgent: bool = False
    is_private: bool = False
    requires_confirmation: bool = False


@dataclass
class IntegrationConfig:
    """Configuration for platform integrations"""
    platform_type: PlatformType
    api_credentials: Dict[str, str] = field(default_factory=dict)
    webhook_url: Optional[str] = None
    rate_limits: Dict[str, int] = field(default_factory=dict)
    
    # Feature flags
    enable_audio_processing: bool = False
    enable_file_analysis: bool = False
    enable_reactions: bool = True
    enable_private_messages: bool = True
    
    # Behavior settings
    auto_join_meetings: bool = True
    respond_to_mentions: bool = True
    moderate_all_channels: bool = False


class BaseIntegration(ABC):
    """
    Abstract base class for platform integrations
    
    Provides common functionality and defines the interface that all
    platform integrations must implement.
    """
    
    def __init__(self, config: IntegrationConfig, moderator_config: ModeratorConfig, gemini_api_key: str):
        """
        Initialize base integration
        
        Args:
            config: Integration-specific configuration
            moderator_config: Moderator configuration
            gemini_api_key: API key for Gemini services
        """
        self.config = config
        self.moderator_config = moderator_config
        self.gemini_api_key = gemini_api_key
        
        # Initialize moderator for each team/meeting
        self.active_moderators: Dict[str, MeetingModerator] = {}
        
        # Message processing
        self.message_handlers: Dict[MessageType, Callable] = {}
        self.event_handlers: Dict[str, Callable] = {}
        
        # Rate limiting and health
        self.rate_limiter = RateLimiter(config.rate_limits)
        self.health_status = {"status": "initializing", "last_check": 0.0}
        
        # Setup default handlers
        self._setup_default_handlers()
        
        logger.info(f"Base integration initialized for {config.platform_type.value}")
    
    @abstractmethod
    async def connect(self) -> bool:
        """
        Connect to the platform
        
        Returns:
            True if connection successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def disconnect(self) -> None:
        """Disconnect from the platform"""
        pass
    
    @abstractmethod
    async def send_message(self, response: PlatformResponse, context: Dict[str, Any]) -> bool:
        """
        Send a message to the platform
        
        Args:
            response: Formatted response to send
            context: Context information (channel, thread, etc.)
            
        Returns:
            True if message sent successfully, False otherwise
        """
        pass
    
    @abstractmethod
    async def join_meeting(self, meeting_id: str) -> bool:
        """
        Join a meeting on the platform
        
        Args:
            meeting_id: Platform-specific meeting identifier
            
        Returns:
            True if joined successfully, False otherwise
        """
        pass
    
    @abstractmethod
    async def leave_meeting(self, meeting_id: str) -> None:
        """
        Leave a meeting on the platform
        
        Args:
            meeting_id: Platform-specific meeting identifier
        """
        pass
    
    async def process_message(self, message: PlatformMessage) -> List[PlatformResponse]:
        """
        Process an incoming message and generate responses
        
        Args:
            message: Incoming platform message
            
        Returns:
            List of responses to send
        """
        try:
            # Skip bot's own messages
            if message.is_bot_message:
                return []
            
            # Rate limiting check
            if not self.rate_limiter.allow_request(message.sender):
                logger.warning(f"Rate limit exceeded for {message.sender}")
                return []
            
            # Get or create moderator for this meeting/channel
            moderator = self._get_or_create_moderator(message)
            
            # Process the message with moderator
            moderation_events = moderator.process_speech(message.sender, message.content)
            
            # Convert moderation events to platform responses
            responses = []
            for event in moderation_events:
                response = self._convert_event_to_response(event, message)
                if response:
                    responses.append(response)
            
            return responses
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return [self._create_error_response(message)]
    
    async def handle_meeting_event(self, event_type: str, event_data: Dict[str, Any]) -> None:
        """
        Handle meeting-related events (start, end, participant join/leave)
        
        Args:
            event_type: Type of meeting event
            event_data: Event-specific data
        """
        try:
            meeting_id = event_data.get('meeting_id')
            if not meeting_id:
                return
            
            if event_type == 'meeting_started':
                await self._handle_meeting_start(meeting_id, event_data)
            elif event_type == 'meeting_ended':
                await self._handle_meeting_end(meeting_id, event_data)
            elif event_type == 'participant_joined':
                await self._handle_participant_join(meeting_id, event_data)
            elif event_type == 'participant_left':
                await self._handle_participant_leave(meeting_id, event_data)
            
        except Exception as e:
            logger.error(f"Error handling meeting event {event_type}: {e}")
    
    def register_message_handler(self, message_type: MessageType, handler: Callable):
        """Register a custom message handler"""
        self.message_handlers[message_type] = handler
    
    def register_event_handler(self, event_type: str, handler: Callable):
        """Register a custom event handler"""
        self.event_handlers[event_type] = handler
    
    async def get_health_status(self) -> Dict[str, Any]:
        """Get current health status of the integration"""
        return {
            **self.health_status,
            'active_moderators': len(self.active_moderators),
            'rate_limiter_status': self.rate_limiter.get_status(),
            'platform': self.config.platform_type.value
        }
    
    def _get_or_create_moderator(self, message: PlatformMessage) -> MeetingModerator:
        """Get existing moderator or create new one for the meeting/channel"""
        
        # Use meeting_id if available, otherwise use channel_id
        context_id = message.meeting_id or message.channel_id or "default"
        
        if context_id not in self.active_moderators:
            # Extract team_id from platform data or use context_id
            team_id = message.platform_data.get('team_id', context_id)
            
            self.active_moderators[context_id] = MeetingModerator(
                config=self.moderator_config,
                gemini_api_key=self.gemini_api_key,
                team_id=team_id
            )
            
            logger.info(f"Created new moderator for context {context_id}")
        
        return self.active_moderators[context_id]
    
    def _convert_event_to_response(self, event: ModerationEvent, original_message: PlatformMessage) -> Optional[PlatformResponse]:
        """Convert a moderation event to a platform response"""
        
        if event.event_type == 'interruption':
            return PlatformResponse(
                message=event.message,
                response_type='text',
                is_urgent=True,
                metadata={
                    'event_type': event.event_type,
                    'confidence': event.confidence,
                    'reason': event.context.get('reason', 'unknown')
                }
            )
        elif event.event_type == 'suggestion':
            return PlatformResponse(
                message=event.message,
                response_type='text',
                is_urgent=False,
                metadata={
                    'event_type': event.event_type,
                    'confidence': event.confidence,
                    'suggestion_type': event.context.get('suggestion_type', 'general')
                }
            )
        elif event.event_type == 'phase_change':
            return PlatformResponse(
                message=event.message,
                response_type='notification',
                metadata={
                    'event_type': event.event_type,
                    'new_phase': event.context.get('new_phase', 'unknown')
                }
            )
        
        return None
    
    def _create_error_response(self, original_message: PlatformMessage) -> PlatformResponse:
        """Create an error response"""
        return PlatformResponse(
            message="I'm having trouble processing that message. Please continue with your meeting.",
            response_type='text',
            is_private=True,
            metadata={'error': True}
        )
    
    async def _handle_meeting_start(self, meeting_id: str, event_data: Dict[str, Any]):
        """Handle meeting start event"""
        participants = event_data.get('participants', [])
        
        if meeting_id in self.active_moderators:
            moderator = self.active_moderators[meeting_id]
            moderator.start_meeting(participants)
            
            # Send welcome message if configured
            if self.config.auto_join_meetings:
                welcome_response = PlatformResponse(
                    message=moderator._get_welcome_message(),
                    response_type='text',
                    metadata={'event': 'meeting_start'}
                )
                await self.send_message(welcome_response, {'meeting_id': meeting_id})
    
    async def _handle_meeting_end(self, meeting_id: str, event_data: Dict[str, Any]):
        """Handle meeting end event"""
        if meeting_id in self.active_moderators:
            moderator = self.active_moderators[meeting_id]
            summary = moderator.end_meeting()
            
            # Send summary if configured
            summary_message = f"Meeting completed! Effectiveness score: {summary['effectiveness_score']:.2f}"
            summary_response = PlatformResponse(
                message=summary_message,
                response_type='text',
                metadata={'event': 'meeting_end', 'summary': summary}
            )
            await self.send_message(summary_response, {'meeting_id': meeting_id})
            
            # Clean up moderator
            del self.active_moderators[meeting_id]
    
    async def _handle_participant_join(self, meeting_id: str, event_data: Dict[str, Any]):
        """Handle participant join event"""
        participant = event_data.get('participant')
        if participant and meeting_id in self.active_moderators:
            logger.info(f"Participant {participant} joined meeting {meeting_id}")
    
    async def _handle_participant_leave(self, meeting_id: str, event_data: Dict[str, Any]):
        """Handle participant leave event"""
        participant = event_data.get('participant')
        if participant and meeting_id in self.active_moderators:
            logger.info(f"Participant {participant} left meeting {meeting_id}")
    
    def _setup_default_handlers(self):
        """Setup default message and event handlers"""
        
        # Default message handlers
        self.message_handlers[MessageType.TEXT] = self._handle_text_message
        self.message_handlers[MessageType.SYSTEM] = self._handle_system_message
        
        # Default event handlers
        self.event_handlers['health_check'] = self._handle_health_check
        self.event_handlers['feedback'] = self._handle_feedback
    
    async def _handle_text_message(self, message: PlatformMessage) -> List[PlatformResponse]:
        """Default text message handler"""
        return await self.process_message(message)
    
    async def _handle_system_message(self, message: PlatformMessage) -> List[PlatformResponse]:
        """Default system message handler"""
        # System messages typically don't need moderation
        return []
    
    async def _handle_health_check(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle health check requests"""
        return await self.get_health_status()
    
    async def _handle_feedback(self, event_data: Dict[str, Any]) -> None:
        """Handle feedback submission"""
        meeting_id = event_data.get('meeting_id')
        if meeting_id and meeting_id in self.active_moderators:
            moderator = self.active_moderators[meeting_id]
            
            moderator.collect_feedback(
                feedback_type=event_data.get('feedback_type', 'overall'),
                rating=event_data.get('rating', 0),
                participant=event_data.get('participant', 'unknown'),
                context=event_data.get('context', {}),
                comment=event_data.get('comment')
            )


class RateLimiter:
    """Simple rate limiter for API requests"""
    
    def __init__(self, limits: Dict[str, int]):
        """
        Initialize rate limiter
        
        Args:
            limits: Dictionary of limit_type -> requests_per_minute
        """
        self.limits = limits
        self.request_counts: Dict[str, List[float]] = {}
    
    def allow_request(self, identifier: str, limit_type: str = 'default') -> bool:
        """
        Check if request is allowed under rate limits
        
        Args:
            identifier: Unique identifier (user, IP, etc.)
            limit_type: Type of rate limit to check
            
        Returns:
            True if request is allowed, False otherwise
        """
        import time
        
        current_time = time.time()
        limit = self.limits.get(limit_type, 60)  # Default 60 requests per minute
        
        # Clean old requests (older than 1 minute)
        key = f"{identifier}:{limit_type}"
        if key not in self.request_counts:
            self.request_counts[key] = []
        
        self.request_counts[key] = [
            req_time for req_time in self.request_counts[key]
            if current_time - req_time < 60.0
        ]
        
        # Check if under limit
        if len(self.request_counts[key]) < limit:
            self.request_counts[key].append(current_time)
            return True
        
        return False
    
    def get_status(self) -> Dict[str, Any]:
        """Get current rate limiter status"""
        return {
            'active_limiters': len(self.request_counts),
            'limits': self.limits
        }
