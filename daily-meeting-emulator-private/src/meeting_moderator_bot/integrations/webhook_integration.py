"""
Webhook Integration

Generic webhook-based integration for the Meeting Moderator Bot.
Allows integration with any platform that can send HTTP webhooks.
"""

import logging
import json
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import asyncio

try:
    from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
    from fastapi.responses import JSONResponse
    import uvicorn
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False

from .base_integration import (
    BaseIntegration, PlatformMessage, PlatformResponse, 
    IntegrationConfig, PlatformType, MessageType
)
from ..utils.config import ModeratorConfig

logger = logging.getLogger(__name__)


@dataclass
class WebhookConfig(IntegrationConfig):
    """Configuration for webhook integration"""
    platform_type: PlatformType = PlatformType.WEBHOOK
    
    # Server settings
    host: str = "0.0.0.0"
    port: int = 8080
    webhook_path: str = "/webhook"
    
    # Security
    webhook_secret: Optional[str] = None
    require_authentication: bool = False
    
    # Response settings
    response_format: str = "json"  # json, text, custom
    include_metadata: bool = True


class WebhookIntegration(BaseIntegration):
    """
    Generic webhook integration for Meeting Moderator Bot
    
    Provides HTTP webhook endpoints for receiving messages and events
    from external platforms and sending responses back.
    """
    
    def __init__(self, config: WebhookConfig, moderator_config: ModeratorConfig, gemini_api_key: str):
        """
        Initialize webhook integration
        
        Args:
            config: Webhook-specific configuration
            moderator_config: Moderator configuration
            gemini_api_key: API key for Gemini services
        """
        super().__init__(config, moderator_config, gemini_api_key)
        
        self.webhook_config = config
        self.app = None
        self.server = None
        
        # Response queue for async processing
        self.response_queue = asyncio.Queue()
        self.response_callbacks: Dict[str, callable] = {}
        
        if FASTAPI_AVAILABLE:
            self._setup_fastapi_app()
        else:
            logger.warning("FastAPI not available. Webhook integration will be limited.")
        
        logger.info("Webhook integration initialized")
    
    def _setup_fastapi_app(self):
        """Setup FastAPI application with webhook endpoints"""
        
        self.app = FastAPI(
            title="Meeting Moderator Bot Webhook",
            description="Webhook endpoints for meeting moderation",
            version="1.0.0"
        )
        
        # Main webhook endpoint
        @self.app.post(self.webhook_config.webhook_path)
        async def webhook_handler(request: Request):
            return await self._handle_webhook_request(request)
        
        # Health check endpoint
        @self.app.get("/health")
        async def health_check():
            status = await self.get_health_status()
            return JSONResponse(content=status)
        
        # Feedback endpoint
        @self.app.post("/feedback")
        async def feedback_handler(request: Request):
            return await self._handle_feedback_request(request)
        
        # Meeting events endpoint
        @self.app.post("/meeting-events")
        async def meeting_events_handler(request: Request):
            return await self._handle_meeting_event_request(request)
    
    async def connect(self) -> bool:
        """
        Start the webhook server
        
        Returns:
            True if server started successfully, False otherwise
        """
        if not FASTAPI_AVAILABLE:
            logger.error("FastAPI not available. Cannot start webhook server.")
            return False
        
        try:
            config = uvicorn.Config(
                app=self.app,
                host=self.webhook_config.host,
                port=self.webhook_config.port,
                log_level="info"
            )
            self.server = uvicorn.Server(config)
            
            # Start server in background task
            asyncio.create_task(self.server.serve())
            
            # Wait a moment for server to start
            await asyncio.sleep(1)
            
            self.health_status["status"] = "connected"
            self.health_status["last_check"] = time.time()
            
            logger.info(f"Webhook server started on {self.webhook_config.host}:{self.webhook_config.port}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start webhook server: {e}")
            return False
    
    async def disconnect(self) -> None:
        """Stop the webhook server"""
        if self.server:
            self.server.should_exit = True
            await asyncio.sleep(1)  # Give server time to shutdown
            
        self.health_status["status"] = "disconnected"
        logger.info("Webhook server stopped")
    
    async def send_message(self, response: PlatformResponse, context: Dict[str, Any]) -> bool:
        """
        Send a response message (for webhook, this queues the response)
        
        Args:
            response: Response to send
            context: Context information
            
        Returns:
            True if response queued successfully, False otherwise
        """
        try:
            # For webhook integration, we queue responses to be picked up
            # by the platform that made the original request
            response_data = {
                'message': response.message,
                'response_type': response.response_type,
                'metadata': response.metadata,
                'timestamp': time.time(),
                'context': context
            }
            
            await self.response_queue.put(response_data)
            
            # If there's a callback for this context, call it
            callback_key = context.get('callback_id')
            if callback_key and callback_key in self.response_callbacks:
                callback = self.response_callbacks[callback_key]
                await callback(response_data)
                del self.response_callbacks[callback_key]
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            return False
    
    async def join_meeting(self, meeting_id: str) -> bool:
        """
        Join a meeting (for webhook, this is a no-op)
        
        Args:
            meeting_id: Meeting identifier
            
        Returns:
            Always True for webhook integration
        """
        logger.info(f"Webhook integration: joining meeting {meeting_id}")
        return True
    
    async def leave_meeting(self, meeting_id: str) -> None:
        """
        Leave a meeting (for webhook, this is a no-op)
        
        Args:
            meeting_id: Meeting identifier
        """
        logger.info(f"Webhook integration: leaving meeting {meeting_id}")
    
    async def _handle_webhook_request(self, request: Request) -> JSONResponse:
        """Handle incoming webhook requests"""
        
        try:
            # Verify authentication if required
            if self.webhook_config.require_authentication:
                if not self._verify_webhook_auth(request):
                    raise HTTPException(status_code=401, detail="Unauthorized")
            
            # Parse request body
            body = await request.body()
            if not body:
                raise HTTPException(status_code=400, detail="Empty request body")
            
            try:
                data = json.loads(body)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="Invalid JSON")
            
            # Convert webhook data to PlatformMessage
            message = self._convert_webhook_to_message(data, request)
            
            # Process message
            responses = await self.process_message(message)
            
            # Format response
            response_data = self._format_webhook_response(responses, data)
            
            return JSONResponse(content=response_data)
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error handling webhook request: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")
    
    async def _handle_feedback_request(self, request: Request) -> JSONResponse:
        """Handle feedback submission requests"""
        
        try:
            body = await request.body()
            data = json.loads(body)
            
            # Extract feedback data
            meeting_id = data.get('meeting_id')
            feedback_type = data.get('feedback_type', 'overall')
            rating = data.get('rating', 0)
            participant = data.get('participant', 'unknown')
            comment = data.get('comment')
            context = data.get('context', {})
            
            # Submit feedback
            await self._handle_feedback({
                'meeting_id': meeting_id,
                'feedback_type': feedback_type,
                'rating': rating,
                'participant': participant,
                'comment': comment,
                'context': context
            })
            
            return JSONResponse(content={'status': 'success', 'message': 'Feedback received'})
            
        except Exception as e:
            logger.error(f"Error handling feedback request: {e}")
            raise HTTPException(status_code=500, detail="Failed to process feedback")
    
    async def _handle_meeting_event_request(self, request: Request) -> JSONResponse:
        """Handle meeting event requests"""
        
        try:
            body = await request.body()
            data = json.loads(body)
            
            event_type = data.get('event_type')
            event_data = data.get('event_data', {})
            
            if not event_type:
                raise HTTPException(status_code=400, detail="Missing event_type")
            
            # Handle meeting event
            await self.handle_meeting_event(event_type, event_data)
            
            return JSONResponse(content={'status': 'success', 'message': 'Event processed'})
            
        except Exception as e:
            logger.error(f"Error handling meeting event request: {e}")
            raise HTTPException(status_code=500, detail="Failed to process event")
    
    def _convert_webhook_to_message(self, data: Dict[str, Any], request: Request) -> PlatformMessage:
        """Convert webhook data to standardized PlatformMessage"""
        
        # Extract message fields with fallbacks
        message_id = data.get('message_id', f"webhook_{int(time.time())}")
        sender = data.get('sender', data.get('user', data.get('from', 'unknown')))
        content = data.get('content', data.get('text', data.get('message', '')))
        timestamp = data.get('timestamp', time.time())
        
        # Determine message type
        message_type = MessageType.TEXT
        if 'type' in data:
            try:
                message_type = MessageType(data['type'])
            except ValueError:
                message_type = MessageType.TEXT
        
        # Extract platform-specific data
        platform_data = {
            'webhook_source': str(request.client.host) if request.client else 'unknown',
            'user_agent': request.headers.get('user-agent', ''),
            'original_data': data
        }
        
        return PlatformMessage(
            message_id=message_id,
            sender=sender,
            content=content,
            timestamp=timestamp,
            message_type=message_type,
            platform_data=platform_data,
            meeting_id=data.get('meeting_id'),
            channel_id=data.get('channel_id'),
            thread_id=data.get('thread_id'),
            is_bot_message=data.get('is_bot', False),
            mentions=data.get('mentions', []),
            attachments=data.get('attachments', [])
        )
    
    def _format_webhook_response(self, responses: List[PlatformResponse], original_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format responses for webhook reply"""
        
        if not responses:
            return {'status': 'success', 'responses': []}
        
        formatted_responses = []
        for response in responses:
            formatted_response = {
                'message': response.message,
                'type': response.response_type,
                'urgent': response.is_urgent,
                'private': response.is_private
            }
            
            if self.webhook_config.include_metadata:
                formatted_response['metadata'] = response.metadata
            
            formatted_responses.append(formatted_response)
        
        return {
            'status': 'success',
            'responses': formatted_responses,
            'original_message_id': original_data.get('message_id'),
            'timestamp': time.time()
        }
    
    def _verify_webhook_auth(self, request: Request) -> bool:
        """Verify webhook authentication"""
        
        if not self.webhook_config.webhook_secret:
            return True  # No secret configured, allow all
        
        # Check for secret in headers
        auth_header = request.headers.get('authorization')
        secret_header = request.headers.get('x-webhook-secret')
        
        if auth_header and auth_header == f"Bearer {self.webhook_config.webhook_secret}":
            return True
        
        if secret_header and secret_header == self.webhook_config.webhook_secret:
            return True
        
        return False
    
    def register_response_callback(self, callback_id: str, callback: callable):
        """Register a callback for handling responses"""
        self.response_callbacks[callback_id] = callback
    
    async def get_queued_responses(self, timeout: float = 1.0) -> List[Dict[str, Any]]:
        """Get queued responses (for polling-based integrations)"""
        responses = []
        
        try:
            while True:
                response = await asyncio.wait_for(self.response_queue.get(), timeout=timeout)
                responses.append(response)
                timeout = 0.1  # Shorter timeout for subsequent responses
        except asyncio.TimeoutError:
            pass  # No more responses available
        
        return responses
