#!/usr/bin/env python3
"""
Test Configuration System

Simple test script to verify configuration loading works.
"""

import sys
import os
from config.meeting_config_loader import MeetingConfigLoader

def main():
    print("🧪 Testing Configuration System")
    print("="*50)
    
    try:
        loader = MeetingConfigLoader()
        print("✅ Configuration loader created successfully")
        
        configs = loader.list_configs()
        print(f"✅ Found {len(configs)} configurations:")
        
        for config_name in configs:
            info = loader.get_config_info(config_name)
            print(f"  📋 {config_name}: {info['name']}")
            print(f"     👥 {info['num_participants']} participants")
            print(f"     💬 {info['offtopic_percent']:.1%} off-topic")
            print(f"     🔧 {info['technical_percent']:.1%} technical")
            print(f"     📝 {info['description']}")
            print()
        
        # Test loading a specific config
        if len(sys.argv) > 1:
            config_name = sys.argv[1]
            config = loader.get_config(config_name)
            
            if config:
                print(f"✅ Successfully loaded '{config_name}' configuration")
                print(f"   Name: {config.name}")
                print(f"   Participants: {len(config.participants)}")
                for p in config.participants:
                    print(f"     - {p.name} ({p.role}): {p.style} style, {p.expertise} expertise")
            else:
                print(f"❌ Configuration '{config_name}' not found")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
