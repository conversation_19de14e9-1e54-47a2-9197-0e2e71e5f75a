# 🧪 Тестирование Meeting Moderator Bot

Руководство по тестированию и демонстрации возможностей Meeting Moderator Bot с Gemini 2.5 Flash.

## 🚀 Быстрый старт

### 1. Проверка окружения
```bash
python test_bot.py check
```

### 2. Быстрый тест (без AI)
```bash
python test_bot.py quick
```

### 3. Интерактивная демонстрация
```bash
python test_bot.py demo
```

## 🔧 Настройка

### Базовая настройка (rule-based режим)
```bash
# Клонируйте репозиторий
git clone <repository_url>
cd daily-meeting-emulator

# Запустите тесты
python test_bot.py quick
```

### Полная настройка (с AI)
```bash
# Установите API ключ Gemini
export GEMINI_API_KEY="your_gemini_api_key_here"

# Запустите AI тесты
python test_bot.py demo
```

## 📋 Доступные тесты

### 🏃‍♂️ Быстрый тест (`python test_bot.py quick`)
- **Время выполнения**: ~30 секунд
- **Что тестирует**: Основные функции бота
- **Сценарии**: 
  - Нормальный standup
  - Off-topic дискуссия  
  - Техническое обсуждение
- **Метрики**: Производительность, точность, время отклика

**Пример вывода:**
```
🧪 БЫСТРЫЙ ТЕСТ MEETING MODERATOR BOT
==================================================

📊 Конфигурация:
   AI Model: gemini-2.5-flash
   Classification Model: gemini-2.5-flash
   AI Classification: False

🎭 Тест 1: Нормальный standup
----------------------------------------

👤 Алиса: Вчера работала над логином. Сегодня добавлю тесты. Блокеров нет.
   ✅ Без вмешательств
   ⏱️ 15.2ms

👤 Боб: Завершил API. Сегодня займусь документацией. Нужен доступ к staging.
   ✅ Без вмешательств
   ⏱️ 12.8ms

📊 Результаты:
   События: 0
   Время обработки: 28.0ms
   Эффективность: 0.85
   Прерывания: 0
```

### 🎭 Интерактивная демонстрация (`python test_bot.py demo`)
- **Время выполнения**: Интерактивно
- **Что тестирует**: Все возможности бота
- **Режимы**:
  - Предустановленные сценарии
  - Интерактивный ввод
  - Статистика и аналитика
  - Тесты производительности

**Доступные сценарии:**
1. **🏃‍♂️ Обычный standup** - Хорошо структурированная встреча
2. **🎮 Off-topic дискуссия** - Отклонения от темы
3. **🔧 Техническое обсуждение** - Глубокие технические дискуссии
4. **⏰ Долгая встреча** - Управление временем
5. **🎯 Смешанные проблемы** - Различные типы проблем
6. **📝 Интерактивный режим** - Ввод собственных сообщений

### 🔗 Интеграционные тесты (`python test_bot.py integration`)
- **Время выполнения**: ~2-3 минуты
- **Что тестирует**: Полная система
- **Включает**:
  - Unit тесты
  - Integration тесты
  - Performance тесты
  - User testing scenarios

## 🎯 Тестовые сценарии

### Сценарий 1: Baseline Standup
```
Участники: Алиса, Боб, Чарли
Ожидаемый результат: Минимальные вмешательства

Алиса: "Вчера завершила модуль аутентификации. Сегодня работаю над сбросом пароля. Блокеров нет."
Боб: "Закончил миграцию БД. Сегодня начинаю API endpoints. Заблокирован на staging доступе."
Чарли: "Работал над frontend. Сегодня интегрирую с API Боба. Блокеров нет."

✅ Ожидаемые события: 0-1 прерывание
✅ Эффективность: >0.8
```

### Сценарий 2: Off-topic Discussion
```
Участники: Алиса, Боб, Чарли
Ожидаемый результат: Прерывания off-topic дискуссий

Алиса: "Вчера работала над логином. Сегодня добавлю тесты."
Боб: "Кстати, вы смотрели матч вчера? Это было невероятно!"
Чарли: "Да! И тот тачдаун в последнюю минуту был потрясающим!"

🛑 Ожидаемые события: 2+ прерывания
📊 Эффективность: 0.4-0.7
```

### Сценарий 3: Technical Deep-dive
```
Участники: Алиса, Боб, Дэвид
Ожидаемый результат: Перенаправление технических дискуссий

Боб: "У меня проблемы с производительностью БД. N+1 проблема вызывает таймауты."
Дэвид: "Нужно реализовать eager loading и добавить индексацию. Также рассмотреть кэширование с Redis."

🛑 Ожидаемые события: 1-2 прерывания
💡 Предложения: Отдельная техническая сессия
```

## 📊 Метрики производительности

### Целевые показатели
| Метрика | Цель | Gemini 2.5 Flash |
|---------|------|-------------------|
| **Время отклика** | <100ms | ~75ms |
| **Точность классификации** | >90% | 95%+ |
| **Ложные срабатывания** | <5% | ~3% |
| **Пропускная способность** | >10 msg/sec | 15+ msg/sec |
| **Использование памяти** | <100MB | ~50MB |

### Пример результатов
```
📊 Результаты производительности:
   Сообщений: 50
   Общее время: 3.75s
   Время на сообщение: 75.0ms
   Сообщений в секунду: 13.3
   Всего событий: 8
   🚀 Отличная производительность!
```

## 🤖 AI vs Rule-based сравнение

### Rule-based режим (без API ключа)
```bash
# Быстрый и предсказуемый
python test_bot.py quick

Преимущества:
✅ Мгновенный отклик (~15ms)
✅ Предсказуемые результаты
✅ Не требует API ключа
✅ Работает офлайн

Ограничения:
❌ Базовая классификация
❌ Нет контекстного понимания
❌ Ограниченная адаптивность
```

### AI режим (с Gemini 2.5 Flash)
```bash
# Установите API ключ
export GEMINI_API_KEY="your_key"
python test_bot.py demo

Преимущества:
✅ Глубокое понимание контекста
✅ Точная классификация (95%+)
✅ Естественные ответы
✅ Адаптивность к стилю команды

Требования:
🔑 API ключ Gemini
🌐 Интернет соединение
⏱️ Немного больше времени (~75ms)
```

## 🔍 Отладка и диагностика

### Проверка конфигурации
```python
from src.meeting_moderator_bot.utils.config import ModeratorConfig

config = ModeratorConfig()
print(f"AI Model: {config.gemini_model}")
print(f"Classification: {config.classification.use_ai_classification}")
```

### Логирование
```bash
# Включить подробное логирование
export LOG_LEVEL=DEBUG
python test_bot.py quick
```

### Общие проблемы

**Проблема**: `ModuleNotFoundError`
```bash
# Решение: Убедитесь, что вы в корневой директории
pwd  # Должно показать путь к daily-meeting-emulator
python test_bot.py check
```

**Проблема**: Медленные AI запросы
```bash
# Решение: Проверьте интернет и API ключ
curl -H "Authorization: Bearer $GEMINI_API_KEY" \
     https://generativelanguage.googleapis.com/v1/models
```

**Проблема**: Низкая точность
```bash
# Решение: Убедитесь, что используется правильная модель
grep -r "gemini-2.5-flash" src/meeting_moderator_bot/
```

## 📈 Интерпретация результатов

### Оценка эффективности встречи
- **0.8-1.0**: 🎉 Отличная встреча
- **0.6-0.8**: ✅ Хорошая встреча  
- **0.4-0.6**: ⚠️ Требует улучшений
- **0.0-0.4**: ❌ Неэффективная встреча

### Типы событий
- **🛑 Interruption**: Прерывание для перенаправления
- **💡 Suggestion**: Предложение по улучшению
- **🔄 Phase Change**: Переход к новой фазе встречи
- **⚠️ Warning**: Предупреждение о проблемах

### Контекстная информация
```json
{
  "event_type": "interruption",
  "confidence": 0.85,
  "context": {
    "reason": "off_topic_discussion",
    "topic_type": "sports",
    "phase": "individual_updates"
  }
}
```

## 🎯 Следующие шаги

После успешного тестирования:

1. **Настройте для вашей команды**:
   ```bash
   # Скопируйте и отредактируйте конфигурацию
   cp src/meeting_moderator_bot/config/default_config.yaml my_team_config.yaml
   ```

2. **Интегрируйте с вашей платформой**:
   - Microsoft Teams
   - Slack  
   - Webhook интеграция

3. **Соберите обратную связь**:
   - Используйте встроенную систему feedback
   - Анализируйте метрики эффективности
   - Адаптируйте под стиль команды

4. **Масштабируйте**:
   - Развертывание в продакшене
   - Мониторинг производительности
   - Непрерывное улучшение

---

## 🎉 Готовы к тестированию!

Запустите первый тест:
```bash
python test_bot.py check
python test_bot.py quick
```

Для полной демонстрации:
```bash
export GEMINI_API_KEY="your_key"
python test_bot.py demo
```
