#!/usr/bin/env python3
"""
Quick Dynamic Demo - EPAM AI Proxy Version
Быстрая версия динамической встречи с использованием EPAM AI Proxy

Упрощенная версия для быстрого тестирования динамической генерации через EPAM AI Proxy.
"""

import os
import sys
import time
import asyncio
import subprocess
import tempfile
from pathlib import Path
from datetime import datetime
import random
from typing import Dict, Any, Optional, List
import json
import aiohttp

# Добавляем путь к проекту
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
sys.path.insert(0, os.path.dirname(__file__))

# Импортируем конфигурационный загрузчик
from config.meeting_config_loader import MeetingConfigLoader, MeetingConfig

class Colors:
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    CYAN = '\033[96m'
    MAGENTA = '\033[95m'
    RESET = '\033[0m'
    GRAY = '\033[90m'
    BOLD = '\033[1m'

class EpamAIClient:
    """EPAM AI Proxy client for OpenAI-compatible API"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://ai-proxy.lab.epam.com/openai/deployments/gemini-2.5-flash/chat/completions"
        self.headers = {
            "Api-Key": api_key,
            "Content-Type": "application/json"
        }
    
    async def generate_content(self, prompt: str, max_tokens: int = 8000, temperature: float = 0.7) -> Any:
        """Generate content using EPAM AI Proxy"""
        payload = {
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": max_tokens,
            "temperature": temperature
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(
                self.base_url,
                headers=self.headers,
                json=payload
            ) as response:
                response_text = await response.text()

                if response.status == 200:
                    try:
                        data = json.loads(response_text)

                        # Create response object similar to Gemini
                        class Response:
                            def __init__(self, text):
                                self.text = text

                        content = data.get("choices", [{}])[0].get("message", {}).get("content", "")

                        # Debug info for empty responses
                        if not content:
                            finish_reason = data.get("choices", [{}])[0].get("finish_reason", "unknown")
                            completion_tokens = data.get("usage", {}).get("completion_tokens", 0)
                            print(f"🔍 Empty response debug - finish_reason: {finish_reason}, completion_tokens: {completion_tokens}")
                            if finish_reason == "length":
                                print(f"🔍 Response truncated due to max_tokens limit")
                            elif finish_reason == "content_filter":
                                print(f"🔍 Response filtered by content policy")

                        return Response(content)
                    except json.JSONDecodeError as e:
                        raise Exception(f"JSON decode error: {e}, Response: {response_text}")
                else:
                    raise Exception(f"API Error {response.status}: {response_text}")

class QuickDynamicDemo:
    def __init__(self, config: Optional[MeetingConfig] = None,
                 offtopic_percent: float = 0.3, technical_percent: float = 0.4,
                 num_participants: int = 3, enable_voice: bool = False,
                 typing_animation: bool = False):
        self.start_time = datetime.now()
        self.ai_client = None
        self.deepgram_client = None
        self.message_count = 0
        self.enable_voice = enable_voice
        self.typing_animation = typing_animation

        # Голосовые модели для участников
        self.voice_models = {
            "Alice": "aura-asteria-en",
            "Bob": "aura-zeus-en",
            "Charlie": "aura-orion-en",
            "MODERATOR": "aura-luna-en"
        }

        # Цвета для участников
        self.colors = {
            "Alice": Colors.BLUE,
            "Bob": Colors.GREEN,
            "Charlie": Colors.MAGENTA,
            "MODERATOR": Colors.YELLOW
        }

        # Используем конфигурацию если предоставлена, иначе параметры
        if config:
            self.config = config
            self.offtopic_percent = config.offtopic_percent
            self.technical_percent = config.technical_percent
            self.max_messages = config.max_messages
            # Загружаем участников из конфигурации (НЕ генерируем заново!)
            self.participants = self._load_participants_from_config(config)
        else:
            # Параметры для управления типами сообщений
            self.offtopic_percent = max(0.0, min(1.0, offtopic_percent))  # 0.0-1.0
            self.technical_percent = max(0.0, min(1.0, technical_percent))  # 0.0-1.0
            self.num_participants = max(2, min(6, num_participants))  # 2-6 участников
            self.max_messages = 6  # Default

            # Генерируем участников динамически только для ручной настройки
            self.participants = self._generate_participants()

        # Применяем глобальные параметры к участникам
        self._apply_global_parameters()

        # Голоса и цвета (генерируются динамически)
        self.voice_models, self.colors = self._generate_voice_and_colors()
        
        # Контекст
        self.context = {
            "phase": "standup",
            "spoken": set(),
            "history": [],
            "topics": [],
            "consecutive_offtopic": 0,  # Счетчик последовательных off-topic сообщений
            "consecutive_technical": 0,  # Счетчик последовательных технических сообщений
            "last_message_types": [],  # Последние типы сообщений для анализа
            "fallback_counters": {},  # Счетчики для ротации fallback сообщений
            "pending_jira_assignment": None,  # Ожидание подтверждения назначения тикета
            "waiting_for_confirmation": False,  # Флаг ожидания подтверждения
            "current_topic": None,  # Текущая тема обсуждения
            "topic_closed_by_moderator": False,  # Флаг что модератор закрыл тему
            "messages_since_topic_closure": 0,  # Счетчик сообщений после закрытия темы
            "created_tickets": [],  # Список созданных тикетов
            "suggested_meetings": [],  # Список предложенных технических митингов
            "pending_meeting_discussion": None,  # Ожидание обсуждения митинга
            "waiting_for_meeting_discussion": False  # Флаг ожидания обсуждения митинга
        }

    def _generate_participants(self) -> Dict[str, Dict[str, Any]]:
        """Generate participants based on num_participants setting"""
        # Available participant templates
        participant_templates = [
            {
                "name": "Alice",
                "style": "concise",
                "expertise": "frontend",
                "base_offtopic_tendency": 0.2,
                "base_technical_tendency": 0.3,
                "voice": "aura-luna-en",
                "color": Colors.GREEN
            },
            {
                "name": "Bob",
                "style": "technical",
                "expertise": "backend",
                "base_offtopic_tendency": 0.1,
                "base_technical_tendency": 0.6,
                "voice": "aura-orion-en",
                "color": Colors.YELLOW
            },
            {
                "name": "Charlie",
                "style": "casual",
                "expertise": "fullstack",
                "base_offtopic_tendency": 0.5,
                "base_technical_tendency": 0.4,
                "voice": "aura-arcas-en",
                "color": Colors.CYAN
            },
            {
                "name": "Diana",
                "style": "analytical",
                "expertise": "devops",
                "base_offtopic_tendency": 0.15,
                "base_technical_tendency": 0.7,
                "voice": "aura-asteria-en",
                "color": Colors.MAGENTA
            },
            {
                "name": "Eve",
                "style": "creative",
                "expertise": "design",
                "base_offtopic_tendency": 0.4,
                "base_technical_tendency": 0.2,
                "voice": "aura-stella-en",
                "color": Colors.RED
            },
            {
                "name": "Frank",
                "style": "methodical",
                "expertise": "qa",
                "base_offtopic_tendency": 0.25,
                "base_technical_tendency": 0.5,
                "voice": "aura-perseus-en",
                "color": Colors.BLUE
            }
        ]

        # Select participants based on num_participants
        selected_templates = participant_templates[:self.num_participants]

        # Convert to participants dict
        participants = {}
        for template in selected_templates:
            name = template["name"]
            participants[name] = {
                "style": template["style"],
                "expertise": template["expertise"],
                "base_offtopic_tendency": template["base_offtopic_tendency"],
                "base_technical_tendency": template["base_technical_tendency"]
            }

        return participants

    def _load_participants_from_config(self, config: MeetingConfig) -> Dict[str, Dict[str, Any]]:
        """Load participants from configuration"""
        participants = {}

        for participant_config in config.participants:
            participants[participant_config.name] = {
                "style": participant_config.style,
                "expertise": participant_config.expertise,
                "role": participant_config.role,
                "base_offtopic_tendency": participant_config.base_offtopic_tendency,
                "base_technical_tendency": participant_config.base_technical_tendency,
                "voice_model": participant_config.voice_model,
                "personality_traits": participant_config.personality_traits
            }

        return participants

    def _generate_voice_and_colors(self) -> tuple:
        """Generate voice models and colors for current participants"""
        participant_templates = [
            {"name": "Alice", "voice": "aura-luna-en", "color": Colors.GREEN},
            {"name": "Bob", "voice": "aura-orion-en", "color": Colors.YELLOW},
            {"name": "Charlie", "voice": "aura-arcas-en", "color": Colors.CYAN},
            {"name": "Diana", "voice": "aura-asteria-en", "color": Colors.MAGENTA},
            {"name": "Eve", "voice": "aura-stella-en", "color": Colors.RED},
            {"name": "Frank", "voice": "aura-perseus-en", "color": Colors.BLUE}
        ]

        voice_models = {"MODERATOR": "aura-asteria-en"}
        colors = {"MODERATOR": Colors.BLUE}

        # Add voices and colors for current participants
        for i, name in enumerate(self.participants.keys()):
            if i < len(participant_templates):
                template = participant_templates[i]
                voice_models[name] = template["voice"]
                colors[name] = template["color"]

        return voice_models, colors

    def _apply_global_parameters(self):
        """Apply global off-topic and technical percentages to participants"""
        for name, participant in self.participants.items():
            # Calculate adjusted tendencies based on global parameters
            base_offtopic = participant["base_offtopic_tendency"]
            base_technical = participant["base_technical_tendency"]

            # Apply global multipliers
            adjusted_offtopic = base_offtopic * (self.offtopic_percent / 0.3)  # 0.3 is default
            adjusted_technical = base_technical * (self.technical_percent / 0.4)  # 0.4 is default

            # Ensure values stay within bounds
            participant["tendency_offtopic"] = max(0.0, min(1.0, adjusted_offtopic))
            participant["tendency_technical"] = max(0.0, min(1.0, adjusted_technical))

            # Normalize so offtopic + technical + standup = reasonable distribution
            total_tendency = participant["tendency_offtopic"] + participant["tendency_technical"]
            if total_tendency > 0.8:  # Leave at least 20% for standup updates
                scale_factor = 0.8 / total_tendency
                participant["tendency_offtopic"] *= scale_factor
                participant["tendency_technical"] *= scale_factor

    def get_time(self) -> str:
        elapsed = datetime.now() - self.start_time
        return f"{elapsed.total_seconds():.0f}s"

    def print_message(self, speaker: str, message: str, typing_animation: bool = False):
        timestamp = self.get_time()
        color = self.colors.get(speaker, Colors.GRAY)
        icon = "🤖" if speaker == "MODERATOR" else "👤"

        print(f"\n{color}[{timestamp}] {icon} {speaker}:{Colors.RESET}")
        print(f"{color}💬 ", end="", flush=True)

        if typing_animation:
            # Анимация печати (медленно)
            for char in message:
                print(char, end="", flush=True)
                time.sleep(0.001)
        else:
            # Быстрый вывод без задержек
            print(message, end="", flush=True)

        print(f"{Colors.RESET}")

    def print_status(self, status: str, details: str = ""):
        timestamp = self.get_time()
        print(f"{Colors.GRAY}[{timestamp}] 🧠 {status}{Colors.RESET}")
        if details:
            print(f"{Colors.GRAY}   📋 {details}{Colors.RESET}")

    def update_message_counters(self, message_type: str):
        """Update consecutive message counters for off-topic and technical discussions"""
        # Добавляем тип сообщения в историю
        self.context["last_message_types"].append(message_type)

        # Оставляем только последние 5 типов сообщений
        if len(self.context["last_message_types"]) > 5:
            self.context["last_message_types"] = self.context["last_message_types"][-5:]

        # Обновляем счетчики последовательных сообщений
        if message_type == "off_topic":
            self.context["consecutive_offtopic"] += 1
            self.context["consecutive_technical"] = 0  # Сбрасываем технический счетчик
        elif message_type in ["technical_discussion", "technical", "question"]:
            # Вопросы тоже считаем как часть технической дискуссии если они о технических темах
            self.context["consecutive_technical"] += 1
            self.context["consecutive_offtopic"] = 0  # Сбрасываем off-topic счетчик
        else:
            # Для других типов сбрасываем оба счетчика
            self.context["consecutive_offtopic"] = 0
            self.context["consecutive_technical"] = 0

    def should_moderator_intervene(self, message_type: str) -> bool:
        """Determine if moderator should intervene based on consecutive messages"""
        # В standup фазе - вмешиваемся сразу при off-topic
        if self.context["phase"] == "standup":
            return message_type in ["off_topic", "technical_discussion", "question"]

        # В discussion фазе - проверяем разные типы вмешательства
        if message_type == "off_topic":
            # Вмешиваемся при 2+ последовательных off-topic
            return self.context["consecutive_offtopic"] >= 2

        elif message_type in ["technical_discussion", "technical", "question"]:
            # Вмешиваемся при 3+ последовательных технических сообщениях
            return self.context["consecutive_technical"] >= 3

        return False

    async def initialize(self):
        print(f"{Colors.CYAN}🚀 QUICK DYNAMIC MEETING DEMO - EPAM AI PROXY{Colors.RESET}")
        print(f"{Colors.CYAN}🤖 Fast AI dialogue generation with EPAM AI Proxy{Colors.RESET}")
        print("="*60)

        # Show participant tendencies
        print(f"{Colors.YELLOW}👥 Participant Tendencies:{Colors.RESET}")
        for name, participant in self.participants.items():
            color = self.colors[name]
            offtopic_pct = participant["tendency_offtopic"] * 100
            technical_pct = participant["tendency_technical"] * 100
            print(f"  {color}{name}: {offtopic_pct:.0f}% off-topic, {technical_pct:.0f}% technical{Colors.RESET}")
        print()

        # EPAM AI Proxy
        api_key = "dial-lxvco7gj9vf9qtqfbvgm2i8v9w7"  # Используем предоставленный ключ
        if api_key:
            try:
                self.ai_client = EpamAIClient(api_key)
                # Тестируем соединение
                print(f"{Colors.YELLOW}🔄 Testing EPAM AI Proxy connection...{Colors.RESET}")
                test_response = await self.ai_client.generate_content("Say hello", max_tokens=4000)
                if test_response and test_response.text:
                    print(f"{Colors.GREEN}✅ EPAM AI Proxy ready{Colors.RESET}")
                    print(f"{Colors.GRAY}   Test response: {test_response.text}{Colors.RESET}")
                else:
                    print(f"{Colors.RED}❌ EPAM AI Proxy test failed - empty response{Colors.RESET}")
                    print(f"{Colors.GRAY}   Response object: {test_response}{Colors.RESET}")
                    return False
            except Exception as e:
                print(f"{Colors.RED}❌ EPAM AI Proxy failed: {e}{Colors.RESET}")
                print(f"{Colors.GRAY}   Error type: {type(e).__name__}{Colors.RESET}")
                import traceback
                print(f"{Colors.GRAY}   Traceback: {traceback.format_exc()}{Colors.RESET}")
                return False
        else:
            print(f"{Colors.RED}❌ EPAM AI Proxy key required{Colors.RESET}")
            return False

        # Инициализируем Deepgram клиент если включена озвучка
        if self.enable_voice:
            try:
                from deepgram import DeepgramClient
                # Используем ключ из переменной окружения или заданный ключ
                deepgram_api_key = os.getenv('DEEPGRAM_API_KEY', '****************************************')
                if deepgram_api_key:
                    self.deepgram_client = DeepgramClient(deepgram_api_key)
                    print(f"{Colors.GREEN}🔊 Voice synthesis enabled with Deepgram{Colors.RESET}")
                else:
                    print(f"{Colors.YELLOW}⚠️ DEEPGRAM_API_KEY not found, voice disabled{Colors.RESET}")
                    self.enable_voice = False
            except ImportError:
                print(f"{Colors.YELLOW}⚠️ Deepgram library not installed, voice disabled{Colors.RESET}")
                self.enable_voice = False
        else:
            print(f"{Colors.GRAY}🔇 Voice synthesis disabled to save costs{Colors.RESET}")

        return True

    async def generate_voice(self, speaker: str, text: str):
        if not self.enable_voice or not self.deepgram_client:
            return

        try:
            from deepgram import SpeakOptions

            options = SpeakOptions(
                model=self.voice_models.get(speaker, "aura-asteria-en"),
                encoding="linear16",
                sample_rate=24000
            )

            import tempfile
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                temp_filename = temp_file.name

            import asyncio
            response = await asyncio.to_thread(
                self.deepgram_client.speak.rest.v("1").save,
                filename=temp_filename,
                source={"text": text},
                options=options
            )

            from pathlib import Path
            if Path(temp_filename).exists():
                color = self.colors.get(speaker, Colors.GRAY)
                print(f"{color}   🔊 {speaker}'s voice{Colors.RESET}")
                import subprocess
                subprocess.run(['paplay', temp_filename], check=True, capture_output=True)
                Path(temp_filename).unlink()

        except Exception as e:
            print(f"{Colors.YELLOW}   ⚠️ Voice generation failed: {e}{Colors.RESET}")
            return

    def build_conversation_context(self, speaker: str) -> str:
        """Build conversation context for AI generation"""
        if not self.context["history"]:
            return "This is the beginning of the meeting."

        # Последние 3 сообщения для контекста
        recent_history = self.context["history"][-3:]
        context_lines = []

        for hist_speaker, hist_message in recent_history:
            context_lines.append(f"{hist_speaker}: {hist_message}")

        context = "Recent conversation:\n" + "\n".join(context_lines)

        # Добавляем информацию о фазе встречи
        if self.context["phase"] == "standup":
            context += f"\n\nCurrent phase: Standup updates. Participants who have spoken: {', '.join(self.context['spoken']) if self.context['spoken'] else 'None yet'}"
        else:
            context += f"\n\nCurrent phase: Discussion phase after standup updates."

        return context

    async def generate_message(self, speaker: str, standup_phase: bool = True) -> str:
        participant = self.participants[speaker]

        # Проверяем, ожидается ли от этого участника обсуждение митинга
        if self.context["waiting_for_meeting_discussion"]:
            return await self.generate_meeting_discussion_response(speaker, self.context["pending_meeting_discussion"])

        # Проверяем, ожидается ли от этого участника подтверждение назначения тикета
        if (self.context["waiting_for_confirmation"] and
            self.context["pending_jira_assignment"] and
            speaker == self.context["pending_jira_assignment"]["assignee"]):
            return await self.generate_jira_confirmation_response(speaker)

        # Определяем тип сообщения на основе фазы встречи и настроек
        if standup_phase and speaker not in self.context["spoken"]:
            message_type = "standup"
        else:
            # В фазе дискуссии проверяем, закрыл ли модератор предыдущую тему
            if self.context.get("topic_closed_by_moderator", False):
                # Модератор закрыл тему - переключаемся на новую тему
                rand = random.random()
                # Увеличиваем вероятность off-topic после вмешательства модератора
                adjusted_offtopic = min(0.8, participant["tendency_offtopic"] + 0.4)

                if rand < adjusted_offtopic:
                    message_type = "offtopic"
                else:
                    message_type = "technical"  # Новая техническая тема

                # Увеличиваем счетчик сообщений после закрытия темы
                self.context["messages_since_topic_closure"] += 1

                # Сбрасываем флаг после 2 сообщений
                if self.context["messages_since_topic_closure"] >= 2:
                    self.context["topic_closed_by_moderator"] = False
                    self.context["messages_since_topic_closure"] = 0
                    print(f"{Colors.GRAY}   🔄 Topic closure period ended - normal generation resumed{Colors.RESET}")
                else:
                    print(f"{Colors.GRAY}   🔄 Topic closed by moderator - switching to {message_type} ({self.context['messages_since_topic_closure']}/2){Colors.RESET}")
            else:
                # Обычная логика для дискуссии
                rand = random.random()
                offtopic_threshold = participant["tendency_offtopic"]

                if rand < offtopic_threshold:
                    message_type = "offtopic"
                else:
                    # В дискуссии только technical или offtopic, НЕ standup
                    message_type = "technical"

        self.print_status(f"Generating for {speaker}", f"Type: {message_type}")

        # Улучшенные промпты с расширенным контекстом
        recent_context = self.build_conversation_context(speaker)

        if message_type == "standup":
            prompt = f"""You are {speaker}, a {participant['expertise']} developer with a {participant['style']} communication style.

CREATIVE STANDUP CHALLENGE:
Generate a unique, authentic standup update that reflects your expertise in {participant['expertise']} development.

AUTHENTICITY GUIDELINES:
- Talk about REAL technical work (specific technologies, frameworks, systems)
- Use your {participant['style']} communication style naturally
- Mention actual development activities (coding, debugging, testing, deploying)
- Be specific about what you worked on and what's next
- Show your personality and expertise
- Occasionally mention issues that might need Jira tickets (bugs, improvements, investigations)
- Sometimes suggest creating tickets for follow-up work or tracking issues

AVOID REPETITIVE PATTERNS:
- Don't start with the same phrases others have used
- Don't copy the structure of previous updates
- Don't use generic placeholder terms
- Don't sound templated or robotic

CONTEXT: {recent_context}

Generate YOUR unique standup update (2-3 sentences) that sounds like a real {participant['expertise']} developer speaking naturally:"""

        elif message_type == "offtopic":
            # В фазе дискуссии участники могут ссылаться друг на друга
            other_participants = [name for name in self.participants.keys() if name != speaker]

            # Проверяем, была ли тема закрыта модератором
            topic_switch_context = ""
            if self.context.get("topic_closed_by_moderator", False):
                topic_switch_context = "\n\nIMPORTANT: The moderator just intervened. Start a completely fresh conversation on a new, lighter topic. Talk about tools, personal experiences, industry trends, or casual topics."

            prompt = f"""You are {speaker}, a {participant['style']} person in a meeting discussion phase.

DISCUSSION PHASE OFF-TOPIC:
Generate a natural off-topic comment or question that builds team rapport and knowledge sharing.

IMPORTANT - DO NOT:
- Give status updates or work reports
- Talk about what you're currently working on
- Mention your current tasks or progress
- Continue any previous technical discussion that was interrupted

INSTEAD - DO:
- Ask work-related questions to team members: {', '.join(other_participants)}
- Share observations about team processes or workflows
- Discuss development tools, technologies, or industry trends
- Ask about learning experiences or interesting discoveries
- Comment on team dynamics or collaboration approaches
- Bring up development best practices or methodologies
- Ask about challenges others are facing in their work
- Share interesting articles, tools, or resources you found
- Discuss team improvement ideas or suggestions
- Ask casual personal questions (weekend plans, hobbies) occasionally

GUIDELINES:
- Use your {participant['style']} communication style
- Keep it brief but engaging
- Make it conversational and team-building
- If moderator just intervened, switch to a completely new topic
- Reference team members: {', '.join(other_participants)}

CONVERSATION CONTEXT:
{recent_context}{topic_switch_context}

Generate a natural off-topic question or comment that introduces a NEW topic:"""

        else:  # technical
            # В фазе дискуссии участники могут ссылаться друг на друга
            other_participants = [name for name in self.participants.keys() if name != speaker]

            # Проверяем, была ли тема закрыта модератором
            topic_switch_context = ""
            if self.context.get("topic_closed_by_moderator", False):
                topic_switch_context = "\n\nIMPORTANT: The moderator just intervened. Start a completely NEW technical topic. Focus on different technical areas, tools, or challenges."

            prompt = f"""You are {speaker}, a {participant['expertise']} expert in a meeting discussion phase.

TECHNICAL DISCUSSION - RAISE NEW ISSUES & QUESTIONS:
Generate a technical question, concern, or problem that needs team discussion.

IMPORTANT - DO NOT:
- Give status updates or work reports
- Talk about what you're currently working on
- Mention your progress or completed tasks
- Continue any previous technical discussion that was interrupted

INSTEAD - DO:
- Raise NEW technical concerns or challenges
- Ask questions about different architecture, implementation, or best practices
- Suggest improvements or optimizations in different areas
- Point out potential issues or risks in other systems
- Ask for opinions on different technical decisions
- Propose solutions to different existing problems
- Question current approaches or methodologies in other areas

EXAMPLES:
- "I'm concerned about the performance of our current API design... should we create a ticket to investigate?"
- "Should we consider using a different caching strategy? Maybe we should track this optimization work."
- "What do you think about the security implications of...? We might need to file a security review ticket."
- "I noticed a potential issue with our database schema... let's create a ticket to address this."
- "Has anyone thought about how we'll handle scaling...? We should probably create some tickets for this."
- "We should create a Jira ticket to track the frontend refactoring work."
- "Let's file a ticket for the API performance investigation."

GUIDELINES:
- Reference team members: {', '.join(other_participants)}
- Use your {participant['style']} communication style
- Be specific about technologies and implementation details
- Focus on problems, questions, and improvements
- If moderator just intervened, start a completely NEW technical topic
- Avoid continuing the previous discussion thread

CONVERSATION CONTEXT:
{recent_context}{topic_switch_context}

Generate a NEW technical question, concern, or problem:"""

        try:
            start_time = time.time()
            response = await self.ai_client.generate_content(prompt, max_tokens=8000, temperature=0.7)

            if response and response.text and response.text.strip():
                message = response.text.strip().replace('"', '')
                # Убираем обрезку сообщений - позволяем полные ответы
                # if len(message) > 500:
                #     message = message[:500] + "..."

                duration = time.time() - start_time
                self.print_status(f"AI Generated for {speaker}", f"Duration: {duration:.2f}s, Length: {len(message)}")
            else:
                duration = time.time() - start_time
                self.print_status(f"AI returned empty for {speaker}", f"Duration: {duration:.2f}s, Using fallback")
                message = self.get_fallback(speaker, message_type)

            # Обновляем контекст
            self.context["history"].append((speaker, message))
            if message_type == "standup":
                self.context["spoken"].add(speaker)

            return message

        except Exception as e:
            duration = time.time() - start_time if 'start_time' in locals() else 0
            self.print_status(f"Generation failed for {speaker}", f"Error: {str(e)[:50]}, Duration: {duration:.2f}s")
            return self.get_fallback(speaker, message_type)

    def get_fallback(self, speaker: str, message_type: str) -> str:
        # Более разнообразные fallback сообщения
        standup_fallbacks = {
            "Alice": [
                "Yesterday I worked on UI components. Today I'm testing user flows.",
                "I completed the frontend styling. Found some performance issues - should we create a ticket?",
                "Yesterday I fixed some UX issues. Today I'm implementing new features.",
                "I finished the component library updates. We might need a ticket to track the accessibility improvements."
            ],
            "Bob": [
                "I optimized the database queries. Today I'm working on API endpoints.",
                "Yesterday I fixed some backend bugs. Found a potential security issue - let's create a ticket for review.",
                "I completed the server configuration. Today I'm working on performance tuning.",
                "Yesterday I worked on data migration. We should probably create a ticket to track the remaining optimization work."
            ],
            "Charlie": [
                "I finished the integration work. Today I'm doing code reviews.",
                "Yesterday I worked on connecting frontend and backend. Found some edge cases we should file tickets for.",
                "I completed the deployment scripts. We might want to create a ticket for monitoring improvements.",
                "Yesterday I fixed some integration bugs. Should we create a ticket to investigate the root cause?"
            ]
        }

        offtopic_fallbacks = {
            "Alice": [
                "What development tools has everyone been trying lately?",
                "Has anyone found any interesting frontend libraries recently?",
                "What's everyone's take on our current development workflow?",
                "Anyone have thoughts on our team's code review process?",
                "What are everyone's plans for the weekend?",
                "Did you see that article about React performance optimization?"
            ],
            "Bob": [
                "How is everyone handling database migrations in their projects?",
                "What do you think about the new API design patterns we've been using?",
                "Has anyone been experimenting with different testing strategies?",
                "Speaking of architecture, what patterns are you finding most useful?",
                "How was everyone's weekend?",
                "Did anyone catch that tech conference livestream yesterday?"
            ],
            "Charlie": [
                "What's everyone's experience with our current deployment process?",
                "Has anyone been following the latest DevOps trends?",
                "What monitoring solutions are you finding most effective?",
                "Anyone have insights on improving our development environment?",
                "By the way, what's everyone listening to while coding?",
                "Speaking of learning, anyone have good tech book recommendations?"
            ]
        }

        technical_fallbacks = {
            "Alice": [
                "Should we create a ticket to update our testing framework?",
                "What do you think about implementing design system components? We should probably track this work.",
                "I think we need to improve our frontend build process - let's create a ticket for this.",
                "Should we file a ticket to investigate our user experience testing approach?"
            ],
            "Bob": [
                "I'm concerned about our current database architecture - should we create a ticket to review it?",
                "Should we consider implementing microservices? We could create tickets to track the migration work.",
                "Are we planning to upgrade our API versioning strategy? Let's create a ticket for this.",
                "We should probably create a ticket to investigate database performance optimization."
            ],
            "Charlie": [
                "Are we planning to refactor the authentication system? We should create tickets to track this work.",
                "What do you think about our current deployment pipeline? Maybe we need a ticket to improve it.",
                "Should we create a ticket to discuss our approach to integration testing?",
                "I think we need to file tickets for the development workflow improvements we've been discussing."
            ]
        }

        # Используем ротацию для избежания повторений
        key = f"{speaker}_{message_type}"
        if key not in self.context["fallback_counters"]:
            self.context["fallback_counters"][key] = 0

        if message_type == "standup":
            options = standup_fallbacks.get(speaker, ["I'm making good progress on my tasks."])
        elif message_type == "offtopic":
            options = offtopic_fallbacks.get(speaker, ["How's everyone doing today?"])
        else:  # technical
            options = technical_fallbacks.get(speaker, ["What do you think about our current approach?"])

        # Выбираем следующее сообщение по порядку
        counter = self.context["fallback_counters"][key]
        message = options[counter % len(options)]
        self.context["fallback_counters"][key] += 1

        self.print_status(f"Using fallback for {speaker}", f"Type: {message_type}, Counter: {counter}")
        return message

    async def analyze_message_with_ai(self, speaker: str, message: str) -> Dict[str, Any]:
        """AI анализ сообщения для определения типа и уместности"""
        if not self.ai_client:
            return {"type": "unknown", "appropriate": True, "confidence": 0.5}

        prompt = f"""Analyze this standup meeting message for appropriateness and type:

Speaker: {speaker}
Message: "{message}"

STANDUP CONTEXT: Daily standups should focus on brief work updates, progress, and blockers.

Classify this message and respond with ONLY a JSON object:
{{
    "type": "standup_update|technical_discussion|off_topic|question|blocker",
    "appropriate_for_standup": true|false,
    "topic_detected": "brief description of main topic",
    "confidence": 0.0-1.0,
    "reason": "brief explanation"
}}

CLASSIFICATION GUIDELINES:
- standup_update: Work progress, completed tasks, current focus
- technical_discussion: Deep technical details, architecture discussions
- off_topic: Non-work topics, personal conversations
- question: Questions to team members
- blocker: Obstacles preventing progress

APPROPRIATENESS CRITERIA:
- Appropriate: Brief work updates, blockers, quick clarifications
- Not appropriate: Long technical discussions, off-topic chat, detailed questions"""

        try:
            response = await self.ai_client.generate_content(prompt, max_tokens=4000, temperature=0.3)

            if response and response.text:
                # Извлекаем JSON из ответа
                text = response.text.strip()
                # Ищем JSON в ответе
                start = text.find('{')
                end = text.rfind('}') + 1
                if start >= 0 and end > start:
                    json_str = text[start:end]
                    return json.loads(json_str)

        except Exception as e:
            self.print_status("AI analysis failed", str(e)[:30])

        # Fallback к простому анализу
        return self.simple_message_analysis(message)

    def simple_message_analysis(self, message: str) -> Dict[str, Any]:
        """Простой анализ как fallback"""
        message_lower = message.lower()

        # Off-topic keywords
        offtopic_words = ["coffee", "weekend", "game", "netflix", "restaurant", "weather", "movie", "sport", "vacation"]
        if any(word in message_lower for word in offtopic_words):
            return {
                "type": "off_topic",
                "appropriate_for_standup": False,
                "topic_detected": "personal/entertainment topic",
                "confidence": 0.8,
                "reason": "Contains off-topic keywords"
            }

        # Technical keywords
        technical_words = ["architecture", "database", "framework", "refactor", "testing", "api", "performance", "security"]
        if any(word in message_lower for word in technical_words) and len(message) > 100:
            return {
                "type": "technical_discussion",
                "appropriate_for_standup": False,
                "topic_detected": "technical deep-dive",
                "confidence": 0.7,
                "reason": "Detailed technical discussion"
            }

        # Technical questions
        if "?" in message and any(word in message_lower for word in ["what", "how", "why", "should"]):
            # Check if it's a technical question
            if any(word in message_lower for word in ["tool", "debug", "code", "api", "database", "performance", "architecture", "framework", "testing"]):
                return {
                    "type": "technical_discussion",
                    "appropriate_for_standup": False,
                    "topic_detected": "technical question",
                    "confidence": 0.8,
                    "reason": "Technical question to team"
                }
            else:
                return {
                    "type": "question",
                    "appropriate_for_standup": False,
                    "topic_detected": "question to team",
                    "confidence": 0.6,
                    "reason": "Contains question"
                }

        # Long messages
        if len(message) > 120:
            return {
                "type": "detailed_update",
                "appropriate_for_standup": False,
                "topic_detected": "verbose update",
                "confidence": 0.5,
                "reason": "Message too long for standup"
            }

        return {
            "type": "standup_update",
            "appropriate_for_standup": True,
            "topic_detected": "work update",
            "confidence": 0.6,
            "reason": "Appears to be appropriate standup content"
        }

    async def generate_moderator_response(self, speaker: str, message: str, analysis: Dict[str, Any]) -> str:
        """Генерация динамического ответа модератора через AI"""
        if not self.ai_client:
            return self.get_fallback_moderator_response(speaker, analysis)

        message_type = analysis["type"]
        topic = analysis["topic_detected"]
        confidence = analysis["confidence"]

        # Контекст встречи
        meeting_context = ""
        if len(self.context["history"]) > 1:
            recent_speakers = [h[0] for h in self.context["history"][-3:]]
            meeting_context = f"Recent speakers: {', '.join(recent_speakers)}. "

        prompt = f"""You are an AI meeting moderator for a daily standup. A participant just said something inappropriate for standup format.

Participant: {speaker}
Their message: "{message}"
Issue detected: {message_type}
Topic: {topic}
Confidence: {confidence:.2f}

Meeting context: {meeting_context}This is a daily standup where people should give brief updates about work.

Generate a polite, professional moderator response that:
1. Acknowledges {speaker}'s contribution positively
2. Gently redirects the conversation back to standup format
3. Suggests appropriate next steps for the topic
4. Is natural and conversational (not robotic)
5. Is concise but complete (2-3 sentences maximum)
6. Uses {speaker}'s name

SPECIAL HANDLING FOR TECHNICAL DISCUSSIONS:
If this is a technical_discussion that has gone on for 3+ messages, suggest:
- "This sounds like a valuable technical discussion. Let's schedule a separate meeting to dive deeper into this."
- "Great technical insights! Let's move this to a dedicated technical session so we can give it proper time."
- "Important technical considerations! Let's create a follow-up meeting to explore this thoroughly."

Response types:
- For technical_discussion: Suggest creating a Jira ticket, scheduling a technical deep-dive, or moving to a separate discussion
- For off_topic: Gently redirect while acknowledging the team bonding aspect
- For question: Suggest addressing in a follow-up meeting or creating an action item
- For detailed_update: Thank for thoroughness, encourage brevity

Moderator suggestions:
- "Let's create a Jira ticket to track this technical discussion"
- "This sounds like a great topic for a separate technical session"
- "Should we schedule a deep-dive meeting for this?"
- "Let's add this to our action items for follow-up"

Respond with ONLY the moderator's message, no quotes or extra text."""

        try:
            self.print_status("Generating moderator response", f"For {message_type} from {speaker}")

            start_time = time.time()
            response = await self.ai_client.generate_content(prompt, max_tokens=4000, temperature=0.7)

            if response and response.text:
                moderator_response = response.text.strip()
                # Очищаем от лишних символов
                moderator_response = moderator_response.replace('"', '').replace("'", "'")
                # Убираем обрезку ответов модератора - позволяем полные ответы
                # if len(moderator_response) > 400:
                #     moderator_response = moderator_response[:400] + "..."

                duration = time.time() - start_time
                self.print_status("Moderator response generated", f"Duration: {duration:.2f}s")

                return moderator_response

        except Exception as e:
            self.print_status("Moderator generation failed", f"Using fallback: {str(e)[:30]}")

        # Fallback к шаблонным ответам
        return self.get_fallback_moderator_response(speaker, analysis)

    def get_fallback_moderator_response(self, speaker: str, analysis: Dict[str, Any]) -> str:
        """Fallback шаблонные ответы модератора"""
        message_type = analysis["type"]
        topic = analysis["topic_detected"]

        fallback_responses = {
            "off_topic": [
                f"That's great team bonding, {speaker}! Let's continue this conversation after our meeting.",
                f"Thanks for sharing, {speaker}! Let's save this discussion for our team chat.",
                f"I love the team connection, {speaker}! Let's refocus on our agenda for now."
            ],
            "technical_discussion": [
                f"Excellent technical point, {speaker}! Let's create a Jira ticket to track this discussion.",
                f"Great insight, {speaker}! Should we schedule a technical deep-dive session for this?",
                f"Important consideration, {speaker}! Let's add this to our action items for follow-up.",
                f"This sounds like a valuable discussion, {speaker}. Let's move this to a separate technical meeting."
            ],
            "question": [
                f"Good question, {speaker}! Let's address this after everyone shares their updates.",
                f"That's worth exploring, {speaker}! Let's circle back to this after standup.",
                f"Interesting question, {speaker}! Let's note this for discussion after updates."
            ],
            "detailed_update": [
                f"Thanks for the detailed update, {speaker}! Let's keep the remaining updates concise.",
                f"Great information, {speaker}! Let's continue with brief updates from everyone else.",
                f"Appreciate the thoroughness, {speaker}! Let's maintain our standup pace."
            ]
        }

        responses = fallback_responses.get(message_type, [f"Thanks, {speaker}! Let's continue with our standup."])
        return random.choice(responses)

    async def get_bot_response(self, speaker: str, message: str) -> str:
        # Используем AI анализ
        analysis = await self.analyze_message_with_ai(speaker, message)

        self.print_status("AI Analysis",
                         f"Type: {analysis['type']}, Appropriate: {analysis['appropriate_for_standup']}, "
                         f"Confidence: {analysis['confidence']:.2f}")

        # Обновляем счетчики сообщений
        self.update_message_counters(analysis['type'])

        # Проверяем нужно ли вмешательство на основе новой логики
        if not analysis["appropriate_for_standup"] and analysis["confidence"] > 0.6:
            should_intervene = self.should_moderator_intervene(analysis['type'])

            # Отладочная информация
            if analysis['type'] == "off_topic":
                print(f"{Colors.GRAY}   🔄 Off-topic counter: {self.context['consecutive_offtopic']}, Phase: {self.context['phase']}, Intervene: {should_intervene}{Colors.RESET}")
            elif analysis['type'] in ["technical_discussion", "technical", "question"]:
                print(f"{Colors.GRAY}   🔧 Technical counter: {self.context['consecutive_technical']}, Phase: {self.context['phase']}, Intervene: {should_intervene}{Colors.RESET}")

            if should_intervene:
                # Сбрасываем счетчики после вмешательства модератора
                self.context["consecutive_offtopic"] = 0
                self.context["consecutive_technical"] = 0
                intervention_type = "technical discussion" if analysis['type'] in ["technical_discussion", "technical"] else "off-topic"
                print(f"{Colors.GRAY}   🔄 Counters reset after moderator intervention ({intervention_type}){Colors.RESET}")

                # Генерируем динамический ответ модератора
                return await self.generate_moderator_response(speaker, message, analysis)

        # Если не нужно вмешиваться, возвращаем None
        return None

    async def generate_moderator_transition(self, next_speaker: str, previous_speaker: str = None) -> str:
        """Генерация перехода модератора к следующему участнику"""
        if not self.ai_client:
            if previous_speaker:
                return f"Thank you, {previous_speaker}! {next_speaker}, you're up next."
            else:
                return f"{next_speaker}, please share your update."

        context = ""
        if previous_speaker:
            context = f"Previous speaker: {previous_speaker}. "

        if len(self.context["spoken"]) == 0:
            context += "This is the start of standup updates."
        elif len(self.context["spoken"]) >= len(self.participants):
            context += "All participants have given their standup updates."
        else:
            remaining = len(self.participants) - len(self.context["spoken"])
            context += f"{remaining} participants still need to give updates."

        prompt = f"""You are an AI meeting moderator for a daily standup. You need to transition to the next speaker.

Next speaker: {next_speaker}
Context: {context}

Generate a brief, natural transition that:
1. Thanks the previous speaker if there was one
2. Calls on {next_speaker} by name
3. Is professional but friendly
4. Keeps the meeting flowing smoothly
5. Is 1 sentence maximum

Respond with ONLY the moderator's transition, no quotes."""

        try:
            response = await self.ai_client.generate_content(prompt, max_tokens=4000, temperature=0.7)

            if response and response.text:
                transition = response.text.strip().replace('"', '')
                return transition

        except Exception as e:
            self.print_status("Transition generation failed", str(e)[:30])

        # Fallback
        if previous_speaker:
            return f"Thank you, {previous_speaker}! {next_speaker}, you're up next."
        else:
            return f"{next_speaker}, please share your update."

    async def generate_moderator_greeting(self, first_speaker: str) -> str:
        """Generate dynamic moderator greeting"""
        participants_list = list(self.participants.keys())

        prompt = f"""You are an AI meeting moderator starting a daily standup meeting.

CONTEXT:
- Team members present: {', '.join(participants_list)}
- First speaker: {first_speaker}
- This is the beginning of the standup meeting

Generate a natural, professional greeting that:
1. Welcomes the team to the standup
2. Sets a positive, productive tone
3. Calls on {first_speaker} to begin
4. Feels authentic and not templated
5. Is 1-2 sentences maximum

AVOID:
- Generic phrases like "Good morning everyone"
- Repetitive patterns
- Overly formal language
- Templated responses

Generate a fresh, natural moderator greeting:"""

        try:
            response = await self.ai_client.generate_content(prompt, max_tokens=4000, temperature=0.8)

            if response and response.text:
                greeting = response.text.strip().replace('"', '')
                return greeting
            else:
                return f"Let's get started with our standup. {first_speaker}, please begin with your update."

        except Exception as e:
            print(f"Error generating moderator greeting: {e}")
            return f"Good morning! Let's start our standup. {first_speaker}, please begin with your update."

    def select_next_speaker(self) -> str:
        """Выбор следующего участника для standup"""
        # В standup порядке - сначала те, кто не говорил
        not_spoken = [name for name in self.participants.keys()
                     if name not in self.context["spoken"]]

        if not_spoken:
            return not_spoken[0]  # Первый из не говоривших
        else:
            # Все дали обновления - возвращаем None чтобы завершить standup
            return None

    def should_continue_standup(self) -> bool:
        """Проверка, должен ли standup продолжаться"""
        # Продолжаем пока не все дали обновления
        return len(self.context["spoken"]) < len(self.participants)

    def get_random_speaker_for_discussion(self) -> str:
        """Выбор случайного участника для дискуссии после standup"""
        return random.choice(list(self.participants.keys()))

    async def generate_jira_confirmation_response(self, speaker: str) -> str:
        """Generate response to Jira ticket assignment confirmation request"""
        pending = self.context["pending_jira_assignment"]
        participant = self.participants[speaker]

        if not self.ai_client:
            # Simple fallback responses
            responses = [
                f"Yes, I can take that on.",
                f"Sure, I'll handle the {pending['title']} ticket.",
                f"Sounds good, I'll take care of that.",
                f"I'm pretty busy, could someone else take it?"
            ]
            return random.choice(responses)

        prompt = f"""You are {speaker}, a {participant['expertise']} developer with a {participant['style']} communication style.

CONTEXT: The meeting moderator just asked you to confirm if you're okay with being assigned a Jira ticket.

TICKET DETAILS:
- Title: "{pending['title']}"
- Suggested by: {pending['original_speaker']}
- You are being asked to take ownership of this work

Generate a natural response that either:
1. ACCEPTS the assignment (70% chance) - show willingness and maybe mention timeline
2. DECLINES politely (30% chance) - explain you're busy and suggest someone else

ACCEPTANCE EXAMPLES:
- "Yes, I can take that on. I should be able to get to it this week."
- "Sure, that fits well with what I'm working on. I'll handle it."
- "Absolutely, I'll create the ticket and start investigating."

DECLINE EXAMPLES:
- "I'm pretty swamped with the current sprint, could someone else take it?"
- "I have a lot on my plate right now, maybe [other participant] could handle it?"

Use your {participant['style']} communication style and keep it brief (1-2 sentences).

Generate your response:"""

        try:
            response = await self.ai_client.generate_content(prompt, max_tokens=4000, temperature=0.7)

            if response and response.text:
                return response.text.strip().replace('"', '')
            else:
                return "Yes, I can take that on."

        except Exception as e:
            print(f"Error generating Jira confirmation response: {e}")
            return "Sure, I'll handle it."

    def extract_addressed_participant(self, message: str) -> str:
        """Extract participant name if someone is directly addressed in the message"""
        message_lower = message.lower()

        # Ищем обращения к участникам по имени
        for participant_name in self.participants.keys():
            name_lower = participant_name.lower()

            # Проверяем различные паттерны обращения
            patterns = [
                f"{name_lower},",  # "Alice,"
                f"{name_lower}?",  # "Alice?"
                f"{name_lower} ",  # "Alice "
                f" {name_lower},", # " Alice,"
                f" {name_lower}?", # " Alice?"
                f" {name_lower} ", # " Alice "
            ]

            for pattern in patterns:
                if pattern in message_lower:
                    return participant_name

        return None

    async def detect_jira_ticket_suggestion(self, message: str, speaker: str = None) -> dict:
        """Detect if message suggests creating a Jira ticket using AI analysis"""
        if not self.ai_client:
            return {"detected": False}

        prompt = f"""Analyze this meeting message to determine the appropriate moderator response.

MESSAGE: "{message}"

Determine if this message suggests:
1. Creating a specific Jira ticket for a concrete task/bug/feature
2. Having a separate technical meeting for complex discussions
3. Neither (just regular discussion)

IMPORTANT DISTINCTION:
- JIRA TICKET: Concrete, actionable work items that can be assigned to someone
  Examples: "I'll create a ticket for the API bug", "We should file an issue for this performance problem"

- TECHNICAL MEETING: Complex discussions with multiple questions, design decisions, or architectural considerations
  Examples: Long messages with multiple questions, design trade-offs, architectural decisions, strategy discussions

- NEITHER: Regular technical discussion, questions, or comments

Respond with JSON format:
{{
    "detected": true/false,
    "response_type": "jira_ticket|technical_meeting|none",
    "confidence": 0.0-1.0,
    "reasoning": "brief explanation",
    "suggested_title": "brief title if detected",
    "work_type": "bug_fix|feature|optimization|investigation|documentation|discussion|strategy",
    "suggestion_type": "self_assign|moderator_create|team_discussion"
}}

Examples:

JIRA TICKET suggestions:
- "I'll open a ticket for this performance issue" → jira_ticket, self_assign
- "We should create a ticket for this API bug" → jira_ticket, moderator_create
- "Let's track this optimization work" → jira_ticket, team_discussion

TECHNICAL MEETING suggestions:
- Long messages with multiple technical questions
- Messages discussing architectural trade-offs
- Strategy discussions with multiple considerations
- Messages with phrases like "we need to sit down and hash out", "design doc", "tech spike"
- Complex discussions about technical criteria or guidelines

NEITHER:
- Simple technical questions
- Regular work updates
- Brief comments or observations

Analyze the message:"""

        try:
            response = await self.ai_client.generate_content(prompt, max_tokens=4000, temperature=0.3)

            if response and response.text:
                try:
                    # Extract JSON from response
                    response_text = response.text.strip()
                    if response_text.startswith('```json'):
                        response_text = response_text.split('```json')[1].split('```')[0]
                    elif response_text.startswith('```'):
                        response_text = response_text.split('```')[1].split('```')[0]

                    analysis = json.loads(response_text)

                    if analysis.get("detected", False) and analysis.get("confidence", 0) > 0.7:
                        response_type = analysis.get("response_type", "jira_ticket")

                        if response_type == "technical_meeting":
                            # Предлагаем технический митинг
                            return {
                                "detected": True,
                                "response_type": "technical_meeting",
                                "title": analysis.get("suggested_title", "Technical discussion"),
                                "confidence": analysis.get("confidence", 0.8),
                                "reasoning": analysis.get("reasoning", "Complex technical discussion detected"),
                                "original_message": message
                            }
                        elif response_type == "jira_ticket":
                            # AI detected a ticket suggestion with high confidence
                            title = analysis.get("suggested_title", self.extract_ticket_title(message))
                            suggestion_type = analysis.get("suggestion_type", "moderator_create")

                            # Для self_assign не нужно назначать, для остальных - предлагаем
                            assignee = None if suggestion_type == "self_assign" else self.suggest_ticket_assignee(message, speaker)

                            return {
                                "detected": True,
                                "response_type": "jira_ticket",
                                "title": title,
                                "suggested_assignee": assignee,
                                "suggestion_type": suggestion_type,
                                "original_message": message,
                                "confidence": analysis.get("confidence", 0.8),
                                "work_type": analysis.get("work_type", "other"),
                                "reasoning": analysis.get("reasoning", "AI detected ticket suggestion")
                            }

                except json.JSONDecodeError:
                    # Fallback if JSON parsing fails
                    pass

        except Exception as e:
            print(f"Error in AI ticket detection: {e}")

        return {"detected": False}

    async def detect_jira_assignment_confirmation(self, speaker: str, message: str) -> dict:
        """Detect if message confirms or declines Jira ticket assignment"""
        if not self.context["waiting_for_confirmation"]:
            return {"detected": False}

        pending = self.context["pending_jira_assignment"]
        if not pending or speaker != pending["assignee"]:
            return {"detected": False}

        if not self.ai_client:
            # Simple keyword fallback
            message_lower = message.lower()
            if any(word in message_lower for word in ["yes", "sure", "okay", "ok", "fine", "agree"]):
                return {"detected": True, "confirmed": True}
            elif any(word in message_lower for word in ["no", "can't", "cannot", "busy", "decline"]):
                return {"detected": True, "confirmed": False}
            return {"detected": False}

        prompt = f"""Analyze this message to determine if the speaker is confirming or declining a Jira ticket assignment.

CONTEXT: {speaker} was asked to take on a Jira ticket titled "{pending['title']}"

MESSAGE: "{message}"

Determine if this message:
1. Confirms acceptance of the ticket assignment
2. Declines the ticket assignment
3. Is unrelated to the assignment question

Respond with JSON format:
{{
    "detected": true/false,
    "confirmed": true/false,
    "confidence": 0.0-1.0,
    "reasoning": "brief explanation"
}}

ACCEPTANCE indicators:
- "Yes", "Sure", "I can take it", "I'll handle it", "Sounds good"
- "I can work on that", "I'll take care of it", "No problem"

DECLINE indicators:
- "No", "I can't", "I'm busy", "Someone else should", "I'm swamped"
- "Not available", "Too much on my plate", "Could [name] do it?"

UNRELATED indicators:
- Technical discussions, questions about other topics, general comments

Analyze the message:"""

        try:
            response = await self.ai_client.generate_content(prompt, max_tokens=4000, temperature=0.3)

            if response and response.text:
                try:
                    response_text = response.text.strip()
                    if response_text.startswith('```json'):
                        response_text = response_text.split('```json')[1].split('```')[0]
                    elif response_text.startswith('```'):
                        response_text = response_text.split('```')[1].split('```')[0]

                    analysis = json.loads(response_text)
                    return analysis

                except json.JSONDecodeError:
                    pass

        except Exception as e:
            print(f"Error in assignment confirmation detection: {e}")

        return {"detected": False}

    def extract_ticket_title(self, message: str) -> str:
        """Extract potential ticket title from message"""
        # Простая эвристика для извлечения темы
        message_clean = message.replace('"', '').replace("'", "")

        # Ищем технические термины и проблемы
        if "performance" in message.lower():
            return "Performance optimization task"
        elif "bug" in message.lower() or "issue" in message.lower():
            return "Bug investigation and fix"
        elif "security" in message.lower():
            return "Security review and improvements"
        elif "test" in message.lower():
            return "Testing framework improvements"
        elif "api" in message.lower():
            return "API development task"
        elif "database" in message.lower():
            return "Database optimization task"
        elif "frontend" in message.lower() or "ui" in message.lower():
            return "Frontend development task"
        elif "backend" in message.lower():
            return "Backend development task"
        else:
            return "Development task from discussion"

    def suggest_ticket_assignee(self, message: str, speaker: str = None) -> str:
        """Suggest who should be assigned to the ticket based on context"""
        message_lower = message.lower()

        # ПРИОРИТЕТ 1: Если speaker предлагает исследование/работу, предлагаем его самого
        suggestion_phrases = [
            "i'm wondering if we should", "should we scope out", "i think we should",
            "we should investigate", "let me investigate", "i'll investigate",
            "i'm thinking", "my concern is", "i've been looking into",
            "might need a ticket", "might need a quick ticket", "will need a ticket",
            "i'll create a ticket", "i'll open a ticket", "i'll file a ticket",
            "should create a ticket", "should open a ticket", "should file a ticket"
        ]

        if speaker:
            for phrase in suggestion_phrases:
                if phrase in message_lower:
                    print(f"{Colors.GRAY}   💡 {speaker} suggested the work ('{phrase}') - assigning to them{Colors.RESET}")
                    return speaker

            # Отладочная информация
            print(f"{Colors.GRAY}   🔍 No suggestion phrases found for {speaker} in: '{message_lower[:100]}...'{Colors.RESET}")

        # ПРИОРИТЕТ 2: Проверяем экспертизу участников по содержанию
        if any(word in message_lower for word in ["frontend", "ui", "react", "vue", "css", "webpack", "vite"]):
            # Ищем frontend разработчика
            for name, participant in self.participants.items():
                if participant["expertise"] in ["frontend", "design"]:
                    return name

        elif any(word in message_lower for word in ["backend", "api", "database", "server", "postgres", "grpc", "redis", "cache"]):
            # Ищем backend разработчика
            for name, participant in self.participants.items():
                if participant["expertise"] in ["backend", "devops"]:
                    return name

        elif any(word in message_lower for word in ["test", "qa", "quality", "playwright"]):
            # Ищем QA специалиста
            for name, participant in self.participants.items():
                if participant["expertise"] == "qa":
                    return name

        # ПРИОРИТЕТ 3: Проверяем прямые упоминания участников в сообщении
        # (только если не speaker сам предложил работу)
        for participant_name in self.participants.keys():
            if participant_name.lower() in message_lower and participant_name != speaker:
                return participant_name

        # ПРИОРИТЕТ 4: Если speaker предложил, но не подходит по экспертизе, все равно предлагаем его
        if speaker:
            return speaker

        # По умолчанию - случайный участник
        return random.choice(list(self.participants.keys()))

    async def generate_discussion_transition(self) -> str:
        """Generate dynamic transition to discussion phase"""
        participants_list = list(self.participants.keys())

        prompt = f"""You are an AI meeting moderator transitioning from standup updates to open discussion.

CONTEXT:
- All team members ({', '.join(participants_list)}) have given their standup updates
- Now opening the floor for questions, discussions, or additional topics
- Want to encourage team collaboration and knowledge sharing

Generate a natural transition that:
1. Acknowledges the completed standup updates
2. Opens the floor for discussion
3. Encourages questions or additional topics
4. Maintains positive energy
5. Is 1-2 sentences maximum

AVOID:
- Generic phrases like "Great updates everyone"
- Repetitive patterns
- Overly formal language
- Templated responses

Generate a fresh, natural discussion transition:"""

        try:
            response = await self.ai_client.generate_content(prompt, max_tokens=4000, temperature=0.8)

            if response and response.text:
                transition = response.text.strip().replace('"', '')
                return transition
            else:
                return "Thanks for the updates! Now let's open the floor for any questions or discussions."

        except Exception as e:
            print(f"Error generating discussion transition: {e}")
            return "Great standup updates everyone! Now let's open the floor for any questions, discussions, or additional topics."

    async def generate_meeting_conclusion(self) -> str:
        """Generate dynamic meeting conclusion"""
        participants_list = list(self.participants.keys())
        meeting_duration = (datetime.now() - self.start_time).total_seconds() / 60

        prompt = f"""You are an AI meeting moderator concluding a daily standup meeting.

CONTEXT:
- Team members: {', '.join(participants_list)}
- Meeting duration: {meeting_duration:.1f} minutes
- {self.message_count} messages exchanged
- Standup and discussion phases completed

Generate a natural conclusion that:
1. Thanks the team for their participation
2. Acknowledges the productive meeting
3. Provides closure to the session
4. Wishes them well for the day
5. Is 1-2 sentences maximum

AVOID:
- Generic phrases like "Great job everyone"
- Repetitive patterns
- Overly formal language
- Templated responses

Generate a fresh, natural meeting conclusion:"""

        try:
            response = await self.ai_client.generate_content(prompt, max_tokens=4000, temperature=0.8)

            if response and response.text:
                conclusion = response.text.strip().replace('"', '')
                return conclusion
            else:
                return "Thank you everyone! That wraps up our standup. Have a productive day!"

        except Exception as e:
            print(f"Error generating meeting conclusion: {e}")
            return "Great job everyone! Thanks for the productive standup."

    async def generate_jira_ticket_response(self, speaker: str, ticket_info: dict) -> str:
        """Generate moderator response for Jira ticket creation"""
        title = ticket_info["title"]
        assignee = ticket_info["suggested_assignee"]
        suggestion_type = ticket_info.get("suggestion_type", "moderator_create")

        if suggestion_type == "self_assign":
            # Speaker will handle it themselves
            prompt = f"""You are an AI meeting moderator responding to someone who said they will create a Jira ticket themselves.

CONTEXT:
- {speaker} said they will create/open a ticket themselves
- They are taking ownership of this work
- Original message: "{ticket_info['original_message']}"

Generate a professional moderator response that:
1. Acknowledges their initiative and ownership
2. Thanks them for being proactive
3. Keeps it concise and supportive
4. Does NOT repeat the ticket details or topic
5. Does NOT offer to create the ticket (they're doing it)
6. Does NOT assign it to anyone (they're handling it)

AVOID:
- Repeating any technical details from their message
- Mentioning the specific ticket topic or title
- Being overly detailed about what they said

GOOD EXAMPLES:
"Thanks for taking the initiative on that, {speaker}!"
"Excellent, {speaker}. Appreciate you taking ownership of that."
"Great, {speaker}! Thanks for being proactive about that."
"Perfect, {speaker}. Thanks for taking care of that."

Generate a natural, brief moderator response:"""

            try:
                response = await self.ai_client.generate_content(prompt, max_tokens=4000, temperature=0.7)

                if response and response.text:
                    return response.text.strip().replace('"', '')
                else:
                    return f"Thanks for taking the initiative on that, {speaker}!"

            except Exception as e:
                print(f"Error generating self-assign response: {e}")
                return f"Great, {speaker}! Thanks for being proactive about that."

        else:
            # Moderator creates and assigns
            prompt = f"""You are an AI meeting moderator responding to a suggestion to create a Jira ticket.

CONTEXT:
- {speaker} suggested creating a ticket
- Topic/area: {title}
- Suggested assignee: {assignee}
- Suggestion type: {suggestion_type}

Generate a professional moderator response that:
1. Acknowledges the good suggestion
2. States you'll create a Jira ticket for this work
3. Briefly mentions the topic/area (NOT the full title)
4. States who you suggest to assign it to
5. Asks that person if they agree to be assigned
6. Keeps it concise and natural

AVOID:
- Repeating the full ticket title verbatim
- Sounding robotic or templated
- Being overly formal

GOOD EXAMPLES:
"Great suggestion, {speaker}! I'll create a ticket for that investigation and suggest assigning it to {assignee}. {assignee}, are you okay with taking this on?"
"Good point, {speaker}! Let me create a ticket to track this work. {assignee}, would you be able to handle this?"
"Excellent idea, {speaker}! I'll file a ticket for this. {assignee}, does that work for you?"

Generate a natural moderator response:"""

        try:
            response = await self.ai_client.generate_content(prompt, max_tokens=4000, temperature=0.7)

            if response and response.text:
                return response.text.strip().replace('"', '')
            else:
                return f"Great suggestion, {speaker}! I'll create a ticket for that and assign it to {assignee}. {assignee}, are you okay with taking this on?"

        except Exception as e:
            print(f"Error generating Jira response: {e}")
            return f"Good idea, {speaker}! I'll create a ticket for that. {assignee}, does that work for you?"

    def build_conversation_context(self, speaker: str) -> str:
        """Build conversation context for AI generation with enhanced addressing detection"""
        if not self.context["history"]:
            return "This is the beginning of the meeting."

        # Если модератор закрыл тему, не передаем контекст предыдущих сообщений
        if self.context.get("topic_closed_by_moderator", False):
            return "The moderator just intervened. Start a fresh conversation on a new topic."

        # Последние 3 сообщения для контекста
        recent_history = self.context["history"][-3:]
        context_lines = []

        for hist_speaker, hist_message in recent_history:
            context_lines.append(f"{hist_speaker}: {hist_message}")

        context = "Recent conversation:\n" + "\n".join(context_lines)

        # Добавляем информацию о фазе встречи
        if self.context["phase"] == "standup":
            context += f"\n\nCurrent phase: Standup updates. Participants who have spoken: {', '.join(self.context['spoken']) if self.context['spoken'] else 'None yet'}"
        else:
            context += f"\n\nCurrent phase: Discussion phase after standup updates."

        # Проверяем, был ли speaker упомянут в последнем сообщении
        addressed_context = ""
        if len(self.context["history"]) > 0:
            last_speaker, last_message = self.context["history"][-1]
            if speaker.lower() in last_message.lower() and last_speaker != speaker:
                addressed_context = f"\n\nIMPORTANT: {last_speaker} just addressed you directly in their message. You should respond to their question or comment."

            # Добавляем контекст о технических темах для лучшего построения диалога
            if any(word in last_message.lower() for word in ["tools", "plugins", "debugging", "performance", "api", "testing"]):
                addressed_context += f"\nCONTEXT: The conversation is about technical topics. Try to build on or respond to what {last_speaker} mentioned."

        return context + addressed_context

    def print_meeting_summary(self):
        """Print summary of created tickets and suggested meetings"""
        print(f"\n{Colors.BOLD}📋 MEETING SUMMARY{Colors.RESET}")
        print("=" * 60)

        # Jira Tickets
        tickets = self.context["created_tickets"]
        if tickets:
            print(f"\n{Colors.YELLOW}🎫 JIRA TICKETS TO BE CREATED ({len(tickets)}){Colors.RESET}")
            for i, ticket in enumerate(tickets, 1):
                type_icon = "👤" if ticket["type"] == "self_assign" else "👥"
                print(f"{i:2d}. {type_icon} {Colors.CYAN}{ticket['title']}{Colors.RESET}")
                print(f"     📝 Suggested by: {ticket['suggested_by']}")
                print(f"     👤 Assignee: {ticket['assignee']}")
                print(f"     🏷️  Type: {ticket['type']}")
                print()
        else:
            print(f"\n{Colors.GRAY}🎫 No Jira tickets were suggested{Colors.RESET}")

        # Technical Meetings
        meetings = self.context["suggested_meetings"]
        if meetings:
            print(f"\n{Colors.BLUE}🏢 TECHNICAL MEETINGS TO BE SCHEDULED ({len(meetings)}){Colors.RESET}")
            for i, meeting in enumerate(meetings, 1):
                print(f"{i:2d}. 📅 {Colors.CYAN}{meeting['topic']}{Colors.RESET}")
                print(f"     📝 Suggested by: {meeting['suggested_by']}")
                if 'discussed_by' in meeting:
                    print(f"     💬 Logistics discussed by: {meeting['discussed_by']}")
                if 'attendees' in meeting:
                    print(f"     👥 Attendees: {meeting['attendees']}")
                print(f"     🏷️  Type: {meeting['type']}")
                print()
        else:
            print(f"\n{Colors.GRAY}🏢 No technical meetings were suggested{Colors.RESET}")

        print("=" * 60)



    async def generate_technical_meeting_response(self, speaker: str, suggestion_info: dict) -> tuple[str, list[str]]:
        """Generate moderator response for technical meeting suggestion"""
        topic = suggestion_info.get("title", "technical discussion")

        # Предлагаем участников для митинга
        participants = list(self.participants.keys())
        other_participants = [name for name in participants if name != speaker]

        if not self.ai_client:
            return f"Great point, {speaker}! This sounds like a valuable technical discussion. I suggest we include {', '.join(other_participants)} in this meeting. Does that work for you?", participants

        prompt = f"""You are an AI meeting moderator responding to a complex technical discussion that needs a separate meeting.

CONTEXT:
- {speaker} raised a complex technical topic: {topic}
- Available team members: {', '.join(participants)}
- You need to suggest who should attend the meeting

Generate a professional moderator response that:
1. Acknowledges the valuable technical discussion
2. Suggests scheduling a separate technical meeting
3. Proposes specific attendees (suggest including most or all team members)
4. Asks the initiator if they agree with the proposed attendees
5. Keeps it concise and natural

GOOD EXAMPLES:
"Excellent points, {speaker}! This deserves a dedicated technical session. I suggest we include {', '.join(other_participants)} in this meeting. Does that work for you?"
"Great technical discussion, {speaker}! Let's schedule a separate meeting with {', '.join(other_participants)}. Are you okay with that group?"
"Thanks for raising this, {speaker}! I think we should set up a technical deep-dive with the whole team. Sound good?"

Generate a natural moderator response that suggests attendees:"""

        try:
            response = await self.ai_client.generate_content(prompt, max_tokens=4000, temperature=0.7)

            if response and response.text:
                return response.text.strip().replace('"', ''), participants
            else:
                return f"Great technical discussion, {speaker}! I suggest we include {', '.join(other_participants)} in this meeting. Does that work for you?", participants

        except Exception as e:
            print(f"Error generating technical meeting response: {e}")
            return f"Excellent points, {speaker}! Let's schedule a separate meeting with {', '.join(other_participants)}. Sound good?", participants

    async def generate_meeting_discussion_response(self, speaker: str, meeting_info: dict) -> str:
        """Generate participant response for meeting discussion"""
        topic = meeting_info.get("topic", "technical discussion")

        if not self.ai_client:
            return f"Sounds good! That group works for me."

        prompt = f"""You are {speaker} responding to a moderator's suggestion about attendees for a technical meeting.

CONTEXT:
- Moderator suggested scheduling a technical meeting about: {topic}
- Moderator proposed specific attendees for the meeting
- You need to give a brief response about whether you agree with the proposed attendees

Generate a brief, positive response that:
1. Agrees with the moderator's suggestion
2. Shows you're on board with the meeting
3. Keeps it very short and natural
4. Optionally mentions timing (this week, next week, etc.)

GOOD EXAMPLES:
"Sounds good! That group works for me."
"Perfect! I agree with those attendees."
"Yes, that makes sense. Let's schedule it for next week."
"Good idea! I'm on board with that."
"Agreed! That's the right group for this discussion."

Generate a brief, positive response:"""

        try:
            response = await self.ai_client.generate_content(prompt, max_tokens=4000, temperature=0.7)

            if response and response.text:
                return response.text.strip().replace('"', '')
            else:
                return f"Sounds good! That group works for me."

        except Exception as e:
            print(f"Error generating meeting discussion response: {e}")
            return f"Perfect! I agree with those attendees."

    async def run_quick_demo(self, max_messages: int = 8):
        if not await self.initialize():
            return

        print(f"\n{Colors.CYAN}🎬 Starting dynamic meeting simulation...{Colors.RESET}")

        # Начинаем с приветствия модератора
        first_speaker = self.select_next_speaker()
        greeting = await self.generate_moderator_greeting(first_speaker)
        self.print_message("MODERATOR", greeting, self.typing_animation)
        await self.generate_voice("MODERATOR", greeting)

        # Основной цикл с управлением фазами встречи
        previous_speaker = None
        current_speaker = first_speaker
        standup_phase = True
        addressed_participant = None  # Кто был упомянут в предыдущем сообщении

        for i in range(max_messages):
            # Проверяем фазу встречи
            if standup_phase and not self.should_continue_standup() and not self.context["waiting_for_confirmation"] and not self.context["waiting_for_meeting_discussion"]:
                # Все дали standup обновления и не ожидаем подтверждения, переходим к дискуссии
                standup_phase = False
                self.context["phase"] = "discussion"  # Обновляем фазу в контексте
                self.context["consecutive_offtopic"] = 0  # Сбрасываем счетчик
                print(f"\n{Colors.YELLOW}🔄 Transitioning to discussion phase{Colors.RESET}")

                # Модератор объявляет переход к дискуссии
                discussion_transition = await self.generate_discussion_transition()
                self.print_message("MODERATOR", discussion_transition, self.typing_animation)
                await self.generate_voice("MODERATOR", discussion_transition)

                current_speaker = self.get_random_speaker_for_discussion()

            # Выбираем участника
            if i == 0:
                speaker = current_speaker
            else:
                if standup_phase:
                    # В фазе standup - проверяем ожидание подтверждения
                    if self.context["waiting_for_confirmation"]:
                        # Если ожидаем подтверждения, следующим должен говорить назначаемый
                        speaker = self.context["pending_jira_assignment"]["assignee"]
                        print(f"{Colors.CYAN}   ⏳ {speaker} should respond to Jira assignment{Colors.RESET}")
                    else:
                        # Выбираем следующего кто не говорил
                        next_speaker = self.select_next_speaker()
                        if next_speaker is None:
                            # Все дали обновления, завершаем standup
                            standup_phase = False
                            if i < max_messages - 1:  # Если есть место для дискуссии
                                speaker = self.get_random_speaker_for_discussion()
                            else:
                                break
                        else:
                            speaker = next_speaker
                else:
                    # В фазе дискуссии - проверяем приоритеты
                    if self.context["waiting_for_meeting_discussion"]:
                        # Если ожидаем обсуждения митинга, выбираем случайного участника
                        speaker = self.get_random_speaker_for_discussion()
                        print(f"{Colors.CYAN}   ⏳ {speaker} should discuss meeting logistics{Colors.RESET}")
                    elif self.context["waiting_for_confirmation"]:
                        # Если ожидаем подтверждения, следующим должен говорить назначаемый
                        speaker = self.context["pending_jira_assignment"]["assignee"]
                        print(f"{Colors.CYAN}   ⏳ {speaker} should respond to Jira assignment{Colors.RESET}")
                    elif addressed_participant:
                        # Если кто-то был упомянут, он должен ответить
                        speaker = addressed_participant
                        addressed_participant = None  # Сбрасываем после использования
                        print(f"{Colors.YELLOW}   👤 {speaker} responds to direct address{Colors.RESET}")
                    else:
                        # Случайный выбор
                        speaker = self.get_random_speaker_for_discussion()

                # Модератор управляет переходами только в standup фазе и если не ожидает подтверждения
                if standup_phase and not self.context["waiting_for_confirmation"] and not self.context["waiting_for_meeting_discussion"]:
                    transition = await self.generate_moderator_transition(speaker, previous_speaker)
                    self.print_message("MODERATOR", transition, self.typing_animation)
                    await self.generate_voice("MODERATOR", transition)
                # В фазе дискуссии участники говорят спонтанно без вызова модератора

            # Генерируем сообщение участника
            message = await self.generate_message(speaker, standup_phase)

            # Показываем сообщение участника
            self.print_message(speaker, message, self.typing_animation)
            await self.generate_voice(speaker, message)

            # В фазе дискуссии проверяем обращения к участникам
            if not standup_phase:
                addressed_participant = self.extract_addressed_participant(message)
                if addressed_participant:
                    print(f"{Colors.CYAN}   📞 {speaker} addressed {addressed_participant}{Colors.RESET}")

            # Проверяем обсуждение митинга после генерации сообщения
            if self.context["waiting_for_meeting_discussion"]:
                # Добавляем митинг в список предложенных
                pending_meeting = self.context["pending_meeting_discussion"]
                self.context["suggested_meetings"].append({
                    "topic": pending_meeting['topic'],
                    "suggested_by": pending_meeting['suggested_by'],
                    "type": "technical_meeting",
                    "discussed_by": speaker,
                    "attendees": ", ".join(pending_meeting.get('attendees', []))
                })

                # Сбрасываем состояние ожидания
                self.context["pending_meeting_discussion"] = None
                self.context["waiting_for_meeting_discussion"] = False

                # Теперь устанавливаем флаг закрытия темы
                self.context["topic_closed_by_moderator"] = True
                self.context["current_topic"] = None
                self.context["messages_since_topic_closure"] = 0
                print(f"{Colors.GRAY}   🔄 Meeting discussed - topic closed, next messages will start fresh{Colors.RESET}")

            # Проверяем подтверждение назначения Jira тикета
            elif self.context["waiting_for_confirmation"]:
                assignment_confirmation = await self.detect_jira_assignment_confirmation(speaker, message)
                if assignment_confirmation["detected"]:
                    pending = self.context["pending_jira_assignment"]
                if assignment_confirmation["confirmed"]:
                    print(f"{Colors.GREEN}   ✅ {speaker} confirmed Jira ticket assignment{Colors.RESET}")
                    confirmation_response = f"Perfect! Thanks for taking this on, {speaker}. I'll get that ticket assigned to you."

                    # Добавляем подтвержденный тикет в список созданных
                    pending = self.context["pending_jira_assignment"]
                    self.context["created_tickets"].append({
                        "title": pending['title'],
                        "assignee": pending['assignee'],
                        "type": "moderator_assigned",
                        "suggested_by": pending['original_speaker']
                    })
                else:
                    print(f"{Colors.YELLOW}   ❌ {speaker} declined Jira ticket assignment{Colors.RESET}")
                    confirmation_response = f"No problem, {speaker}! Let me reassign this to someone else. I'll follow up on the assignment later."

                # Сбрасываем состояние ожидания
                self.context["pending_jira_assignment"] = None
                self.context["waiting_for_confirmation"] = False

                # Модератор отвечает на подтверждение
                self.print_message("MODERATOR", confirmation_response, self.typing_animation)
                await self.generate_voice("MODERATOR", confirmation_response)
                self.print_status("Assignment confirmation", f"{speaker} {'accepted' if assignment_confirmation['confirmed'] else 'declined'} ticket")

            # Если не ожидаем подтверждения, проверяем предложения о создании Jira тикетов или технических митингов
            elif not self.context["waiting_for_confirmation"]:
                suggestion = await self.detect_jira_ticket_suggestion(message, speaker)
                if suggestion["detected"]:
                    response_type = suggestion.get("response_type", "jira_ticket")
                    confidence = suggestion.get("confidence", 0)

                    if response_type == "technical_meeting":
                        print(f"{Colors.BLUE}   🏢 Technical meeting suggestion detected (confidence: {confidence:.2f}){Colors.RESET}")
                        print(f"{Colors.GRAY}   📝 Reasoning: {suggestion.get('reasoning', 'N/A')}{Colors.RESET}")

                        # Сбрасываем счетчик off-topic после предложения митинга
                        self.context["consecutive_offtopic"] = 0
                        print(f"{Colors.GRAY}   🔄 Off-topic counter reset after meeting suggestion{Colors.RESET}")

                        # Генерируем ответ модератора о техническом митинге
                        meeting_response, attendees = await self.generate_technical_meeting_response(speaker, suggestion)
                        self.print_message("MODERATOR", meeting_response, self.typing_animation)
                        await self.generate_voice("MODERATOR", meeting_response)
                        self.print_status("Technical meeting suggestion", f"Topic: {suggestion['title']}")

                        # Устанавливаем ожидание обсуждения митинга
                        self.context["pending_meeting_discussion"] = {
                            "topic": suggestion['title'],
                            "suggested_by": speaker,
                            "type": "technical_meeting",
                            "attendees": attendees
                        }
                        self.context["waiting_for_meeting_discussion"] = True
                        print(f"{Colors.CYAN}   ⏳ Waiting for team to discuss meeting logistics{Colors.RESET}")

                        # НЕ устанавливаем флаг закрытия темы - участники должны обсудить митинг

                    elif response_type == "jira_ticket":
                        suggestion_type = suggestion.get("suggestion_type", "moderator_create")
                        print(f"{Colors.YELLOW}   🎫 Jira ticket suggestion detected (confidence: {confidence:.2f}, type: {suggestion_type}){Colors.RESET}")
                        print(f"{Colors.GRAY}   📝 Reasoning: {suggestion.get('reasoning', 'N/A')}{Colors.RESET}")

                        # Сбрасываем счетчик off-topic после Jira предложения
                        self.context["consecutive_offtopic"] = 0
                        print(f"{Colors.GRAY}   🔄 Off-topic counter reset after Jira suggestion{Colors.RESET}")

                        if suggestion_type == "self_assign":
                            # Участник сам создаст тикет - только подтверждаем
                            jira_response = await self.generate_jira_ticket_response(speaker, suggestion)
                            self.print_message("MODERATOR", jira_response, self.typing_animation)
                            await self.generate_voice("MODERATOR", jira_response)
                            self.print_status("Jira self-assignment", f"{speaker} will handle: {suggestion['title']}")

                            # Добавляем тикет в список созданных
                            self.context["created_tickets"].append({
                                "title": suggestion['title'],
                                "assignee": speaker,
                                "type": "self_assign",
                                "suggested_by": speaker
                            })

                            # Устанавливаем флаг что модератор закрыл тему (мягко)
                            self.context["topic_closed_by_moderator"] = True
                            self.context["messages_since_topic_closure"] = 0
                            print(f"{Colors.GRAY}   🔄 Topic acknowledged by moderator - next messages will start fresh{Colors.RESET}")
                        else:
                            # Модератор создает и назначает - ожидаем подтверждения
                            self.context["pending_jira_assignment"] = {
                                "title": suggestion['title'],
                                "assignee": suggestion['suggested_assignee'],
                                "original_speaker": speaker
                            }
                            self.context["waiting_for_confirmation"] = True

                            jira_response = await self.generate_jira_ticket_response(speaker, suggestion)
                            self.print_message("MODERATOR", jira_response, self.typing_animation)
                            await self.generate_voice("MODERATOR", jira_response)
                            self.print_status("Jira ticket creation", f"Title: {suggestion['title']}, Assignee: {suggestion['suggested_assignee']}")
                            print(f"{Colors.CYAN}   ⏳ Waiting for {suggestion['suggested_assignee']} to confirm assignment{Colors.RESET}")

                            # Устанавливаем флаг что модератор закрыл тему
                            self.context["topic_closed_by_moderator"] = True
                            self.context["current_topic"] = None
                            self.context["messages_since_topic_closure"] = 0
                            print(f"{Colors.GRAY}   🔄 Topic closed by moderator - next messages will start fresh{Colors.RESET}")
                else:
                    # Проверяем нужно ли обычное вмешательство модератора
                    bot_response = await self.get_bot_response(speaker, message)
                    if bot_response:
                        self.print_message("MODERATOR", bot_response, self.typing_animation)
                        await self.generate_voice("MODERATOR", bot_response)
                        self.print_status("Bot intervention", f"Responded to {speaker}")

            # В standup фазе отмечаем что участник дал обновление
            if standup_phase:
                self.context["spoken"].add(speaker)
                print(f"{Colors.GRAY}   ✅ {speaker} completed standup update ({len(self.context['spoken'])}/{len(self.participants)}){Colors.RESET}")

            previous_speaker = speaker
            self.message_count += 1

            # Показываем прогресс
            if (i + 1) % 3 == 0:
                elapsed = (datetime.now() - self.start_time).total_seconds() / 60
                phase_text = "Standup" if standup_phase else "Discussion"
                print(f"\n{Colors.GRAY}📊 Progress: {i+1}/{max_messages} messages, {elapsed:.1f}min, Phase: {phase_text}{Colors.RESET}")

        # Завершение
        conclusion = await self.generate_meeting_conclusion()
        # Пауза убрана для быстрого демо
        self.print_message("MODERATOR", conclusion, self.typing_animation)
        await self.generate_voice("MODERATOR", conclusion)

        # Сводка созданных тикетов и митингов
        self.print_meeting_summary()

        # Статистика
        elapsed = datetime.now() - self.start_time
        print(f"\n{Colors.CYAN}📊 Demo completed in {elapsed.total_seconds():.1f}s{Colors.RESET}")
        print(f"{Colors.CYAN}💬 Total messages: {self.message_count}{Colors.RESET}")
        print(f"{Colors.CYAN}👥 Participants: {len(self.participants)}{Colors.RESET}")

async def main():
    """Основная функция запуска"""
    print(f"{Colors.BLUE}🚀 Quick Dynamic Demo - EPAM AI Proxy Version{Colors.RESET}")
    print(f"{Colors.BLUE}Using EPAM AI Proxy with Gemini-2.5-Flash{Colors.RESET}")
    print(f"{Colors.BLUE}🎫 Enhanced with Jira ticket management and discussion phases{Colors.RESET}")
    print(f"{Colors.GREEN}🔊 Voice synthesis enabled with Deepgram{Colors.RESET}")
    print("="*60)

    # Создаем демо с настройками по умолчанию (быстрый режим)
    demo = QuickDynamicDemo(
        offtopic_percent=0.3,    # 30% склонность к off-topic
        technical_percent=0.4,   # 40% склонность к техническим дискуссиям
        num_participants=3,      # 3 участника
        enable_voice=True,       # Включаем голосовую озвучку
        typing_animation=False   # Отключаем анимацию печати для скорости
    )

    try:
        await demo.run_quick_demo(max_messages=12)  # Оптимальное количество для демо с timeout
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}⏹️ Demo interrupted by user{Colors.RESET}")
    except Exception as e:
        print(f"\n{Colors.RED}❌ Demo failed: {e}{Colors.RESET}")

if __name__ == "__main__":
    asyncio.run(main())
