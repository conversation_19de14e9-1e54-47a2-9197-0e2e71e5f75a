# ElevenLabs Integration Guide

## 🎙️ Quick Dynamic Demo with ElevenLabs Voice Synthesis

Эта версия демо использует высококачественный голосовой синтез от ElevenLabs вместо Deepgram.

### ✅ Преимущества ElevenLabs:

- **Более естественные голоса** с лучшей интонацией
- **Поддержка эмоций** и выразительности  
- **Многоязычность** включая русский без акцента
- **Высокое качество** аудио (MP3 формат)
- **Автоматический выбор голосов** из доступных

### 🔑 Настройка API ключа:

1. **Получите API ключ** на [ElevenLabs](https://elevenlabs.io/)
2. **Установите переменную окружения:**
   ```bash
   export ELEVENLABS_API_KEY='your_elevenlabs_api_key_here'
   ```

### 🧪 Тестирование интеграции:

```bash
# Тест ElevenLabs API и голосового синтеза
python test_elevenlabs.py
```

Этот скрипт:
- Проверит API ключ
- Получит список доступных голосов
- Сгенерирует тестовое аудио
- Воспроизведет его (если доступно)

### 🚀 Запуск демо:

```bash
# Полное демо с ElevenLabs голосами
python quick_dynamic_demo_epam_11labs.py
```

### 🎭 Назначение голосов:

Система автоматически:
1. **Получает список** доступных голосов через API
2. **Назначает голоса** участникам по порядку
3. **Показывает назначения** в консоли
4. **Использует fallback** если API недоступен

### 📋 Пример вывода:

```
🔊 Voice synthesis enabled with ElevenLabs
   📋 Found 25 available voices
   🎙️ Alice: Bella (EXAVITQu...)
   🎙️ Bob: Josh (VR6AewLT...)
   🎙️ Charlie: Adam (pNInz6ob...)
   🎙️ MODERATOR: Rachel (21m00Tcm...)
```

### 🔧 Технические детали:

#### **API Endpoints:**
- **Voices**: `GET /v2/voices` - получение списка голосов
- **TTS**: `POST /v1/text-to-speech/{voice_id}` - генерация речи

#### **Модели ElevenLabs v2.5:**
- **По умолчанию**: `eleven_turbo_v2_5` (баланс качества и скорости)
- **Высокое качество**: `eleven_multilingual_v2` (максимальное качество)
- **Низкая латентность**: `eleven_flash_v2_5` (максимальная скорость)
- **Формат**: `mp3_44100_128` (44.1kHz, 128kbps)
- **Оптимизация латентности**: уровень 1

#### **Voice Settings:**
- **Stability**: 0.5 (баланс стабильности)
- **Similarity Boost**: 0.75 (сходство с оригиналом)
- **Style**: 0.0 (нейтральный стиль)
- **Speaker Boost**: true (улучшение качества)

### 🛠️ Устранение неполадок:

#### **Ошибка API ключа:**
```
❌ ELEVENLABS_API_KEY not found, voice disabled
```
**Решение:** Установите переменную окружения с вашим API ключом

#### **Ошибка воспроизведения:**
```
⚠️ Could not play audio (paplay not available)
```
**Решение:** Установите `pulseaudio-utils` или используйте другой аудио плеер

#### **Ошибка получения голосов:**
```
⚠️ Could not get voices list, using fallback
```
**Решение:** Проверьте интернет соединение и валидность API ключа

### 💡 Советы по использованию:

1. **Квота API**: ElevenLabs имеет лимиты на количество символов в месяц
2. **Кэширование**: Рассмотрите кэширование аудио для повторяющихся фраз
3. **Качество**: Используйте более высокие битрейты для лучшего качества
4. **Языки**: Укажите `language_code` для лучшего произношения

### 🤖 Доступные модели ElevenLabs:

| Модель | Качество | Скорость | Латентность | Использование |
|--------|----------|----------|-------------|---------------|
| **eleven_turbo_v2_5** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ~250ms | **По умолчанию** - баланс |
| **eleven_flash_v2_5** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ~75ms | Реалтайм приложения |
| **eleven_multilingual_v2** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ~500ms | Максимальное качество |
| **eleven_v3** | ⭐⭐⭐⭐⭐ | ⭐⭐ | ~1000ms | Альфа, требует доступа |

### 🔄 Сравнение с Deepgram:

| Параметр | ElevenLabs | Deepgram |
|----------|------------|----------|
| Качество голоса | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| Естественность | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| Скорость | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| Цена | ⭐⭐⭐ | ⭐⭐⭐⭐ |
| Языки | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |

### 📞 Поддержка:

- **Документация**: [ElevenLabs API Docs](https://elevenlabs.io/docs)
- **Сообщество**: [Discord](https://discord.gg/elevenlabs)
- **Помощь**: [Help Center](https://help.elevenlabs.io/)
