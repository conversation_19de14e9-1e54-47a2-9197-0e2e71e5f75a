# Meeting Configuration Templates
# Different meeting scenarios for testing and repeatability

default:
  name: "Default 3-Person Team"
  description: "Standard small team standup"
  max_messages: 10
  offtopic_percent: 0.8
  technical_percent: 0.2
  participants:
    - name: "<PERSON>"
      role: "Frontend Developer"
      style: "concise"
      expertise: "frontend"
      base_offtopic_tendency: 0.8
      base_technical_tendency: 0.2
      voice_model: "aura-luna-en"
      personality_traits:
        - "detail-oriented"
        - "practical" 
        - "solution-focused"
    
    - name: "<PERSON>"
      role: "Backend Developer"
      style: "technical"
      expertise: "backend"
      base_offtopic_tendency: 0.9
      base_technical_tendency: 0.1
      voice_model: "aura-orion-en"
      personality_traits:
        - "perfectionist"
        - "loves technical discussions"
        - "methodical"
    
    - name: "<PERSON>"
      role: "Fullstack Developer"
      style: "casual"
      expertise: "fullstack"
      base_offtopic_tendency: 0.5
      base_technical_tendency: 0.4
      voice_model: "aura-arcas-en"
      personality_traits:
        - "curious"
        - "asks questions"
        - "brings up tangents"

large_team:
  name: "Large Development Team"
  description: "5-person cross-functional team"
  max_messages: 10
  offtopic_percent: 0.4
  technical_percent: 0.5
  participants:
    - name: "<PERSON>"
      role: "Frontend Lead"
      style: "concise"
      expertise: "frontend"
      base_offtopic_tendency: 0.2
      base_technical_tendency: 0.3
      voice_model: "aura-luna-en"
      personality_traits:
        - "leadership-oriented"
        - "efficient"
        - "user-focused"
    
    - name: "Bob"
      role: "Backend Architect"
      style: "technical"
      expertise: "backend"
      base_offtopic_tendency: 0.1
      base_technical_tendency: 0.7
      voice_model: "aura-orion-en"
      personality_traits:
        - "architecture-focused"
        - "performance-oriented"
        - "detailed"
    
    - name: "Charlie"
      role: "Fullstack Developer"
      style: "casual"
      expertise: "fullstack"
      base_offtopic_tendency: 0.6
      base_technical_tendency: 0.4
      voice_model: "aura-arcas-en"
      personality_traits:
        - "social"
        - "collaborative"
        - "adaptable"
    
    - name: "Diana"
      role: "DevOps Engineer"
      style: "analytical"
      expertise: "devops"
      base_offtopic_tendency: 0.15
      base_technical_tendency: 0.8
      voice_model: "aura-asteria-en"
      personality_traits:
        - "systems-thinking"
        - "reliability-focused"
        - "proactive"
    
    - name: "Eve"
      role: "UX Designer"
      style: "creative"
      expertise: "design"
      base_offtopic_tendency: 0.4
      base_technical_tendency: 0.2
      voice_model: "aura-stella-en"
      personality_traits:
        - "user-empathy"
        - "creative-thinking"
        - "visual-oriented"

startup_team:
  name: "Startup Team"
  description: "Fast-paced startup environment"
  max_messages: 8
  offtopic_percent: 0.5
  technical_percent: 0.3
  participants:
    - name: "Alex"
      role: "CTO"
      style: "strategic"
      expertise: "architecture"
      base_offtopic_tendency: 0.3
      base_technical_tendency: 0.5
      voice_model: "aura-perseus-en"
      personality_traits:
        - "big-picture"
        - "decision-maker"
        - "time-conscious"
    
    - name: "Sam"
      role: "Full-stack Developer"
      style: "energetic"
      expertise: "fullstack"
      base_offtopic_tendency: 0.4
      base_technical_tendency: 0.4
      voice_model: "aura-arcas-en"
      personality_traits:
        - "fast-paced"
        - "multi-tasking"
        - "startup-minded"
    
    - name: "Jordan"
      role: "Product Developer"
      style: "product-focused"
      expertise: "product"
      base_offtopic_tendency: 0.6
      base_technical_tendency: 0.2
      voice_model: "aura-luna-en"
      personality_traits:
        - "customer-focused"
        - "feature-oriented"
        - "business-aware"

enterprise_team:
  name: "Enterprise Team"
  description: "Large enterprise development team"
  max_messages: 12
  offtopic_percent: 0.2
  technical_percent: 0.6
  participants:
    - name: "Robert"
      role: "Senior Architect"
      style: "formal"
      expertise: "architecture"
      base_offtopic_tendency: 0.1
      base_technical_tendency: 0.8
      voice_model: "aura-orion-en"
      personality_traits:
        - "enterprise-focused"
        - "compliance-aware"
        - "documentation-oriented"
    
    - name: "Jennifer"
      role: "Tech Lead"
      style: "structured"
      expertise: "leadership"
      base_offtopic_tendency: 0.15
      base_technical_tendency: 0.6
      voice_model: "aura-asteria-en"
      personality_traits:
        - "team-oriented"
        - "process-focused"
        - "mentoring"
    
    - name: "Michael"
      role: "Senior Developer"
      style: "experienced"
      expertise: "backend"
      base_offtopic_tendency: 0.2
      base_technical_tendency: 0.7
      voice_model: "aura-perseus-en"
      personality_traits:
        - "experienced"
        - "quality-focused"
        - "best-practices"
    
    - name: "Sarah"
      role: "QA Engineer"
      style: "methodical"
      expertise: "qa"
      base_offtopic_tendency: 0.25
      base_technical_tendency: 0.5
      voice_model: "aura-luna-en"
      personality_traits:
        - "quality-focused"
        - "detail-oriented"
        - "risk-aware"

chaotic_team:
  name: "Chaotic Team"
  description: "High off-topic, lots of interruptions"
  max_messages: 10
  offtopic_percent: 0.7
  technical_percent: 0.6
  participants:
    - name: "Casey"
      role: "Developer"
      style: "scattered"
      expertise: "frontend"
      base_offtopic_tendency: 0.8
      base_technical_tendency: 0.3
      voice_model: "aura-arcas-en"
      personality_traits:
        - "easily-distracted"
        - "social"
        - "tangential"
    
    - name: "Riley"
      role: "Developer"
      style: "rambling"
      expertise: "backend"
      base_offtopic_tendency: 0.6
      base_technical_tendency: 0.8
      voice_model: "aura-orion-en"
      personality_traits:
        - "over-explains"
        - "technical-deep-dives"
        - "verbose"
    
    - name: "Taylor"
      role: "Developer"
      style: "interruptive"
      expertise: "fullstack"
      base_offtopic_tendency: 0.7
      base_technical_tendency: 0.5
      voice_model: "aura-luna-en"
      personality_traits:
        - "interrupts-frequently"
        - "adds-tangents"
        - "high-energy"

focused_team:
  name: "Highly Focused Team"
  description: "Very disciplined, minimal off-topic"
  max_messages: 6
  offtopic_percent: 0.1
  technical_percent: 0.3
  participants:
    - name: "Morgan"
      role: "Senior Developer"
      style: "disciplined"
      expertise: "backend"
      base_offtopic_tendency: 0.05
      base_technical_tendency: 0.3
      voice_model: "aura-orion-en"
      personality_traits:
        - "highly-focused"
        - "efficient"
        - "time-conscious"
    
    - name: "Quinn"
      role: "Frontend Developer"
      style: "efficient"
      expertise: "frontend"
      base_offtopic_tendency: 0.1
      base_technical_tendency: 0.25
      voice_model: "aura-luna-en"
      personality_traits:
        - "goal-oriented"
        - "concise"
        - "professional"
    
    - name: "Avery"
      role: "DevOps Engineer"
      style: "systematic"
      expertise: "devops"
      base_offtopic_tendency: 0.08
      base_technical_tendency: 0.4
      voice_model: "aura-asteria-en"
      personality_traits:
        - "systematic"
        - "reliable"
        - "process-oriented"
