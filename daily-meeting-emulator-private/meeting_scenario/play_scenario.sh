#!/bin/bash
# Meeting Scenario Playback
echo 'Playing meeting scenario...'
echo 'Playing: 01_meeting_start.wav'
aplay '/home/<USER>/src/daily-meeting-emulator/meeting_scenario/01_meeting_start.wav'
sleep 1
echo 'Playing: 02_alice_update.wav'
aplay '/home/<USER>/src/daily-meeting-emulator/meeting_scenario/02_alice_update.wav'
sleep 1
echo 'Playing: 03_transition_to_bob.wav'
aplay '/home/<USER>/src/daily-meeting-emulator/meeting_scenario/03_transition_to_bob.wav'
sleep 1
echo 'Playing: 04_bob_update.wav'
aplay '/home/<USER>/src/daily-meeting-emulator/meeting_scenario/04_bob_update.wav'
sleep 1
echo 'Playing: 05_blocker_noted.wav'
aplay '/home/<USER>/src/daily-meeting-emulator/meeting_scenario/05_blocker_noted.wav'
sleep 1
echo 'Playing: 06_charlie_technical.wav'
aplay '/home/<USER>/src/daily-meeting-emulator/meeting_scenario/06_charlie_technical.wav'
sleep 1
echo 'Playing: 07_redirect_technical.wav'
aplay '/home/<USER>/src/daily-meeting-emulator/meeting_scenario/07_redirect_technical.wav'
sleep 1
echo 'Playing: 08_meeting_end.wav'
aplay '/home/<USER>/src/daily-meeting-emulator/meeting_scenario/08_meeting_end.wav'
sleep 1
echo 'Scenario complete!'
