#!/usr/bin/env python3
"""
Quick Dynamic Demo - Быстрая версия динамической встречи

Упрощенная версия для быстрого тестирования динамической генерации.
"""

import os
import sys
import time
import asyncio
import subprocess
import tempfile
from pathlib import Path
from datetime import datetime
import random
from typing import Dict, Any, Optional

# Добавляем путь к проекту
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
sys.path.insert(0, os.path.dirname(__file__))

# Импортируем конфигурационный загрузчик
from config.meeting_config_loader import MeetingConfigLoader, MeetingConfig

class Colors:
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    CYAN = '\033[96m'
    MAGENTA = '\033[95m'
    RESET = '\033[0m'
    GRAY = '\033[90m'

class QuickDynamicDemo:
    def __init__(self, config: Optional[MeetingConfig] = None,
                 offtopic_percent: float = 0.3, technical_percent: float = 0.4,
                 num_participants: int = 3):
        self.start_time = datetime.now()
        self.gemini_client = None
        self.deepgram_client = None
        self.message_count = 0

        # Используем конфигурацию если предоставлена, иначе параметры
        if config:
            self.config = config
            self.offtopic_percent = config.offtopic_percent
            self.technical_percent = config.technical_percent
            self.max_messages = config.max_messages
            # Загружаем участников из конфигурации (НЕ генерируем заново!)
            self.participants = self._load_participants_from_config(config)
        else:
            # Параметры для управления типами сообщений
            self.offtopic_percent = max(0.0, min(1.0, offtopic_percent))  # 0.0-1.0
            self.technical_percent = max(0.0, min(1.0, technical_percent))  # 0.0-1.0
            self.num_participants = max(2, min(6, num_participants))  # 2-6 участников
            self.max_messages = 6  # Default

            # Генерируем участников динамически только для ручной настройки
            self.participants = self._generate_participants()

        # Применяем глобальные параметры к участникам
        self._apply_global_parameters()

        # Голоса и цвета (генерируются динамически)
        self.voice_models, self.colors = self._generate_voice_and_colors()
        
        # Контекст
        self.context = {
            "phase": "standup",
            "spoken": set(),
            "history": [],
            "topics": [],
            "consecutive_offtopic": 0,  # Счетчик последовательных off-topic сообщений
            "consecutive_technical": 0,  # Счетчик последовательных технических сообщений
            "last_message_types": [],  # Последние типы сообщений для анализа
            "fallback_counters": {},  # Счетчики для ротации fallback сообщений
            "pending_jira_assignment": None,  # Ожидание подтверждения назначения тикета
            "waiting_for_confirmation": False  # Флаг ожидания подтверждения
        }

    def _generate_participants(self) -> Dict[str, Dict[str, Any]]:
        """Generate participants based on num_participants setting"""
        # Available participant templates
        participant_templates = [
            {
                "name": "Alice",
                "style": "concise",
                "expertise": "frontend",
                "base_offtopic_tendency": 0.2,
                "base_technical_tendency": 0.3,
                "voice": "aura-luna-en",
                "color": Colors.GREEN
            },
            {
                "name": "Bob",
                "style": "technical",
                "expertise": "backend",
                "base_offtopic_tendency": 0.1,
                "base_technical_tendency": 0.6,
                "voice": "aura-orion-en",
                "color": Colors.YELLOW
            },
            {
                "name": "Charlie",
                "style": "casual",
                "expertise": "fullstack",
                "base_offtopic_tendency": 0.5,
                "base_technical_tendency": 0.4,
                "voice": "aura-arcas-en",
                "color": Colors.CYAN
            },
            {
                "name": "Diana",
                "style": "analytical",
                "expertise": "devops",
                "base_offtopic_tendency": 0.15,
                "base_technical_tendency": 0.7,
                "voice": "aura-asteria-en",
                "color": Colors.MAGENTA
            },
            {
                "name": "Eve",
                "style": "creative",
                "expertise": "design",
                "base_offtopic_tendency": 0.4,
                "base_technical_tendency": 0.2,
                "voice": "aura-stella-en",
                "color": Colors.RED
            },
            {
                "name": "Frank",
                "style": "methodical",
                "expertise": "qa",
                "base_offtopic_tendency": 0.25,
                "base_technical_tendency": 0.5,
                "voice": "aura-perseus-en",
                "color": Colors.BLUE
            }
        ]

        # Select participants based on num_participants
        selected_templates = participant_templates[:self.num_participants]

        # Convert to participants dict
        participants = {}
        for template in selected_templates:
            name = template["name"]
            participants[name] = {
                "style": template["style"],
                "expertise": template["expertise"],
                "base_offtopic_tendency": template["base_offtopic_tendency"],
                "base_technical_tendency": template["base_technical_tendency"]
            }

        return participants

    def _load_participants_from_config(self, config: MeetingConfig) -> Dict[str, Dict[str, Any]]:
        """Load participants from configuration"""
        participants = {}

        for participant_config in config.participants:
            participants[participant_config.name] = {
                "style": participant_config.style,
                "expertise": participant_config.expertise,
                "role": participant_config.role,
                "base_offtopic_tendency": participant_config.base_offtopic_tendency,
                "base_technical_tendency": participant_config.base_technical_tendency,
                "voice_model": participant_config.voice_model,
                "personality_traits": participant_config.personality_traits
            }

        return participants

    def _generate_voice_and_colors(self) -> tuple:
        """Generate voice models and colors for current participants"""
        participant_templates = [
            {"name": "Alice", "voice": "aura-luna-en", "color": Colors.GREEN},
            {"name": "Bob", "voice": "aura-orion-en", "color": Colors.YELLOW},
            {"name": "Charlie", "voice": "aura-arcas-en", "color": Colors.CYAN},
            {"name": "Diana", "voice": "aura-asteria-en", "color": Colors.MAGENTA},
            {"name": "Eve", "voice": "aura-stella-en", "color": Colors.RED},
            {"name": "Frank", "voice": "aura-perseus-en", "color": Colors.BLUE}
        ]

        voice_models = {"MODERATOR": "aura-asteria-en"}
        colors = {"MODERATOR": Colors.BLUE}

        # Add voices and colors for current participants
        for i, name in enumerate(self.participants.keys()):
            if i < len(participant_templates):
                template = participant_templates[i]
                voice_models[name] = template["voice"]
                colors[name] = template["color"]

        return voice_models, colors

    def _apply_global_parameters(self):
        """Apply global off-topic and technical percentages to participants"""
        for name, participant in self.participants.items():
            # Calculate adjusted tendencies based on global parameters
            base_offtopic = participant["base_offtopic_tendency"]
            base_technical = participant["base_technical_tendency"]

            # Apply global multipliers
            adjusted_offtopic = base_offtopic * (self.offtopic_percent / 0.3)  # 0.3 is default
            adjusted_technical = base_technical * (self.technical_percent / 0.4)  # 0.4 is default

            # Ensure values stay within bounds
            participant["tendency_offtopic"] = max(0.0, min(1.0, adjusted_offtopic))
            participant["tendency_technical"] = max(0.0, min(1.0, adjusted_technical))

            # Normalize so offtopic + technical + standup = reasonable distribution
            total_tendency = participant["tendency_offtopic"] + participant["tendency_technical"]
            if total_tendency > 0.8:  # Leave at least 20% for standup updates
                scale_factor = 0.8 / total_tendency
                participant["tendency_offtopic"] *= scale_factor
                participant["tendency_technical"] *= scale_factor

    def get_time(self) -> str:
        elapsed = datetime.now() - self.start_time
        return f"{elapsed.total_seconds():.0f}s"

    def print_message(self, speaker: str, message: str):
        timestamp = self.get_time()
        color = self.colors.get(speaker, Colors.GRAY)
        icon = "🤖" if speaker == "MODERATOR" else "👤"
        
        print(f"\n{color}[{timestamp}] {icon} {speaker}:{Colors.RESET}")
        print(f"{color}💬 ", end="", flush=True)
        
        for char in message:
            print(char, end="", flush=True)
            time.sleep(0.001)
        print(f"{Colors.RESET}")

    def print_status(self, status: str, details: str = ""):
        timestamp = self.get_time()
        print(f"{Colors.GRAY}[{timestamp}] 🧠 {status}{Colors.RESET}")
        if details:
            print(f"{Colors.GRAY}   📋 {details}{Colors.RESET}")

    def update_message_counters(self, message_type: str):
        """Update consecutive message counters for off-topic and technical discussions"""
        # Добавляем тип сообщения в историю
        self.context["last_message_types"].append(message_type)

        # Оставляем только последние 5 типов сообщений
        if len(self.context["last_message_types"]) > 5:
            self.context["last_message_types"] = self.context["last_message_types"][-5:]

        # Обновляем счетчики последовательных сообщений
        if message_type == "off_topic":
            self.context["consecutive_offtopic"] += 1
            self.context["consecutive_technical"] = 0  # Сбрасываем технический счетчик
        elif message_type in ["technical_discussion", "technical", "question"]:
            # Вопросы тоже считаем как часть технической дискуссии если они о технических темах
            self.context["consecutive_technical"] += 1
            self.context["consecutive_offtopic"] = 0  # Сбрасываем off-topic счетчик
        else:
            # Для других типов сбрасываем оба счетчика
            self.context["consecutive_offtopic"] = 0
            self.context["consecutive_technical"] = 0

    def should_moderator_intervene(self, message_type: str) -> bool:
        """Determine if moderator should intervene based on consecutive messages"""
        # В standup фазе - вмешиваемся сразу при off-topic
        if self.context["phase"] == "standup":
            return message_type in ["off_topic", "technical_discussion", "question"]

        # В discussion фазе - проверяем разные типы вмешательства
        if message_type == "off_topic":
            # Вмешиваемся при 2+ последовательных off-topic
            return self.context["consecutive_offtopic"] >= 2

        elif message_type in ["technical_discussion", "technical", "question"]:
            # Вмешиваемся при 3+ последовательных технических сообщениях
            return self.context["consecutive_technical"] >= 3

        return False

    async def initialize(self):
        print(f"{Colors.CYAN}🚀 QUICK DYNAMIC MEETING DEMO{Colors.RESET}")
        print(f"{Colors.CYAN}🤖 Fast AI dialogue generation with voice{Colors.RESET}")
        print("="*60)

        # Show participant tendencies
        print(f"{Colors.YELLOW}👥 Participant Tendencies:{Colors.RESET}")
        for name, participant in self.participants.items():
            color = self.colors[name]
            offtopic_pct = participant["tendency_offtopic"] * 100
            technical_pct = participant["tendency_technical"] * 100
            print(f"  {color}{name}: {offtopic_pct:.0f}% off-topic, {technical_pct:.0f}% technical{Colors.RESET}")
        print()
        
        # Gemini
        gemini_key = os.getenv('GEMINI_API_KEY')
        if gemini_key:
            try:
                import google.generativeai as genai
                genai.configure(api_key=gemini_key)
                self.gemini_client = genai.GenerativeModel('gemini-2.5-flash')
                print(f"{Colors.GREEN}✅ Gemini ready{Colors.RESET}")
            except Exception as e:
                print(f"{Colors.RED}❌ Gemini failed: {e}{Colors.RESET}")
                return False
        else:
            print(f"{Colors.RED}❌ GEMINI_API_KEY required{Colors.RESET}")
            return False
        
        # Deepgram
        deepgram_key = os.getenv('DEEPGRAM_API_KEY')
        if deepgram_key:
            try:
                from deepgram import DeepgramClient
                self.deepgram_client = DeepgramClient(deepgram_key)
                print(f"{Colors.GREEN}✅ Deepgram ready{Colors.RESET}")
            except Exception as e:
                print(f"{Colors.YELLOW}⚠️ Deepgram failed: {e}{Colors.RESET}")
        
        return True

    async def generate_voice(self, speaker: str, text: str):
        if not self.deepgram_client:
            return
        
        try:
            from deepgram import SpeakOptions
            
            options = SpeakOptions(
                model=self.voice_models.get(speaker, "aura-asteria-en"),
                encoding="linear16",
                sample_rate=24000
            )
            
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                temp_filename = temp_file.name
            
            response = await asyncio.to_thread(
                self.deepgram_client.speak.rest.v("1").save,
                filename=temp_filename,
                source={"text": text},
                options=options
            )
            
            if Path(temp_filename).exists():
                color = self.colors.get(speaker, Colors.GRAY)
                print(f"{color}   🔊 {speaker}'s voice{Colors.RESET}")
                subprocess.run(['paplay', temp_filename], check=True, capture_output=True)
                Path(temp_filename).unlink()
                
        except Exception as e:
            self.print_status("Voice failed", str(e)[:30])

    async def generate_message(self, speaker: str, standup_phase: bool = True) -> str:
        participant = self.participants[speaker]

        # Проверяем, ожидается ли от этого участника подтверждение назначения тикета
        if (self.context["waiting_for_confirmation"] and
            self.context["pending_jira_assignment"] and
            speaker == self.context["pending_jira_assignment"]["assignee"]):
            return await self.generate_jira_confirmation_response(speaker)

        # Определяем тип сообщения на основе фазы встречи и настроек
        if standup_phase and speaker not in self.context["spoken"]:
            message_type = "standup"
        else:
            # В фазе дискуссии используем только offtopic и technical типы
            rand = random.random()
            offtopic_threshold = participant["tendency_offtopic"]

            if rand < offtopic_threshold:
                message_type = "offtopic"
            else:
                # В дискуссии только technical или offtopic, НЕ standup
                message_type = "technical"
        
        self.print_status(f"Generating for {speaker}", f"Type: {message_type}")
        
        # Улучшенные промпты с расширенным контекстом
        recent_context = self.build_conversation_context(speaker)

        if message_type == "standup":
            prompt = f"""You are {speaker}, a {participant['expertise']} developer with a {participant['style']} communication style.

CREATIVE STANDUP CHALLENGE:
Generate a unique, authentic standup update that reflects your expertise in {participant['expertise']} development.

AUTHENTICITY GUIDELINES:
- Talk about REAL technical work (specific technologies, frameworks, systems)
- Use your {participant['style']} communication style naturally
- Mention actual development activities (coding, debugging, testing, deploying)
- Be specific about what you worked on and what's next
- Show your personality and expertise
- Occasionally mention issues that might need Jira tickets (bugs, improvements, investigations)
- Sometimes suggest creating tickets for follow-up work or tracking issues

AVOID REPETITIVE PATTERNS:
- Don't start with the same phrases others have used
- Don't copy the structure of previous updates
- Don't use generic placeholder terms
- Don't sound templated or robotic

CONTEXT: {recent_context}

Generate YOUR unique standup update (2-3 sentences) that sounds like a real {participant['expertise']} developer speaking naturally:"""

        elif message_type == "offtopic":
            # В фазе дискуссии участники могут ссылаться друг на друга
            other_participants = [name for name in self.participants.keys() if name != speaker]

            prompt = f"""You are {speaker}, a {participant['style']} person in a meeting discussion phase.

DISCUSSION PHASE OFF-TOPIC:
Generate a natural off-topic comment or question that builds team rapport and knowledge sharing.

IMPORTANT - DO NOT:
- Give status updates or work reports
- Talk about what you're currently working on
- Mention your current tasks or progress

INSTEAD - DO:
- Ask work-related questions to team members: {', '.join(other_participants)}
- Share observations about team processes or workflows
- Discuss development tools, technologies, or industry trends
- Ask about learning experiences or interesting discoveries
- Comment on team dynamics or collaboration approaches
- Bring up development best practices or methodologies
- Ask about challenges others are facing in their work
- Share interesting articles, tools, or resources you found
- Discuss team improvement ideas or suggestions
- Ask casual personal questions (weekend plans, hobbies) occasionally

GUIDELINES:
- Use your {participant['style']} communication style
- Keep it brief but engaging
- Make it conversational and team-building
- RESPOND TO or BUILD ON what others just said when relevant
- If someone asked a question, try to answer it or engage with their topic
- Reference team members: {', '.join(other_participants)}

CONVERSATION CONTEXT:
{recent_context}

Generate a natural off-topic question or comment that fits the conversation flow:"""

        else:  # technical
            # В фазе дискуссии участники могут ссылаться друг на друга
            other_participants = [name for name in self.participants.keys() if name != speaker]

            prompt = f"""You are {speaker}, a {participant['expertise']} expert in a meeting discussion phase.

TECHNICAL DISCUSSION - RAISE ISSUES & QUESTIONS:
Generate a technical question, concern, or problem that needs team discussion.

IMPORTANT - DO NOT:
- Give status updates or work reports
- Talk about what you're currently working on
- Mention your progress or completed tasks

INSTEAD - DO:
- Raise technical concerns or challenges
- Ask questions about architecture, implementation, or best practices
- Suggest improvements or optimizations
- Point out potential issues or risks
- Ask for opinions on technical decisions
- Propose solutions to existing problems
- Question current approaches or methodologies

EXAMPLES:
- "I'm concerned about the performance of our current API design... should we create a ticket to investigate?"
- "Should we consider using a different caching strategy? Maybe we should track this optimization work."
- "What do you think about the security implications of...? We might need to file a security review ticket."
- "I noticed a potential issue with our database schema... let's create a ticket to address this."
- "Has anyone thought about how we'll handle scaling...? We should probably create some tickets for this."
- "We should create a Jira ticket to track the frontend refactoring work."
- "Let's file a ticket for the API performance investigation."

GUIDELINES:
- Reference team members: {', '.join(other_participants)}
- Use your {participant['style']} communication style
- Be specific about technologies and implementation details
- Focus on problems, questions, and improvements
- RESPOND TO or BUILD ON what others just said when relevant
- If someone asked a question, try to answer it or engage with their topic

CONVERSATION CONTEXT:
{recent_context}

Generate a technical question, concern, or problem that fits the conversation flow:"""
        
        try:
            start_time = time.time()
            response = await asyncio.to_thread(
                self.gemini_client.generate_content, prompt
            )
            
            if response and response.text:
                message = response.text.strip().replace('"', '')
                # Увеличиваем лимит для более полных сообщений
                if len(message) > 500:
                    message = message[:500] + "..."
            else:
                message = self.get_fallback(speaker, message_type)
            
            duration = time.time() - start_time
            self.print_status(f"Generated for {speaker}", f"Duration: {duration:.2f}s")
            
            # Обновляем контекст
            self.context["history"].append((speaker, message))
            if message_type == "standup":
                self.context["spoken"].add(speaker)
            
            return message
            
        except Exception as e:
            self.print_status(f"Generation failed for {speaker}", str(e)[:30])
            return self.get_fallback(speaker, message_type)

    def get_fallback(self, speaker: str, message_type: str) -> str:
        # Более разнообразные fallback сообщения
        standup_fallbacks = {
            "Alice": [
                "Yesterday I worked on UI components. Today I'm testing user flows.",
                "I completed the frontend styling. Found some performance issues - should we create a ticket?",
                "Yesterday I fixed some UX issues. Today I'm implementing new features.",
                "I finished the component library updates. We might need a ticket to track the accessibility improvements."
            ],
            "Bob": [
                "I optimized the database queries. Today I'm working on API endpoints.",
                "Yesterday I fixed some backend bugs. Found a potential security issue - let's create a ticket for review.",
                "I completed the server configuration. Today I'm working on performance tuning.",
                "Yesterday I worked on data migration. We should probably create a ticket to track the remaining optimization work."
            ],
            "Charlie": [
                "I finished the integration work. Today I'm doing code reviews.",
                "Yesterday I worked on connecting frontend and backend. Found some edge cases we should file tickets for.",
                "I completed the deployment scripts. We might want to create a ticket for monitoring improvements.",
                "Yesterday I fixed some integration bugs. Should we create a ticket to investigate the root cause?"
            ]
        }

        offtopic_fallbacks = {
            "Alice": [
                "What development tools has everyone been trying lately?",
                "Has anyone found any interesting frontend libraries recently?",
                "What's everyone's take on our current development workflow?",
                "Anyone have thoughts on our team's code review process?",
                "What are everyone's plans for the weekend?",
                "Did you see that article about React performance optimization?"
            ],
            "Bob": [
                "How is everyone handling database migrations in their projects?",
                "What do you think about the new API design patterns we've been using?",
                "Has anyone been experimenting with different testing strategies?",
                "Speaking of architecture, what patterns are you finding most useful?",
                "How was everyone's weekend?",
                "Did anyone catch that tech conference livestream yesterday?"
            ],
            "Charlie": [
                "What's everyone's experience with our current deployment process?",
                "Has anyone been following the latest DevOps trends?",
                "What monitoring solutions are you finding most effective?",
                "Anyone have insights on improving our development environment?",
                "By the way, what's everyone listening to while coding?",
                "Speaking of learning, anyone have good tech book recommendations?"
            ]
        }

        technical_fallbacks = {
            "Alice": [
                "Should we create a ticket to update our testing framework?",
                "What do you think about implementing design system components? We should probably track this work.",
                "I think we need to improve our frontend build process - let's create a ticket for this.",
                "Should we file a ticket to investigate our user experience testing approach?"
            ],
            "Bob": [
                "I'm concerned about our current database architecture - should we create a ticket to review it?",
                "Should we consider implementing microservices? We could create tickets to track the migration work.",
                "Are we planning to upgrade our API versioning strategy? Let's create a ticket for this.",
                "We should probably create a ticket to investigate database performance optimization."
            ],
            "Charlie": [
                "Are we planning to refactor the authentication system? We should create tickets to track this work.",
                "What do you think about our current deployment pipeline? Maybe we need a ticket to improve it.",
                "Should we create a ticket to discuss our approach to integration testing?",
                "I think we need to file tickets for the development workflow improvements we've been discussing."
            ]
        }

        # Используем ротацию для избежания повторений
        key = f"{speaker}_{message_type}"
        if key not in self.context["fallback_counters"]:
            self.context["fallback_counters"][key] = 0

        if message_type == "standup":
            options = standup_fallbacks.get(speaker, ["I'm making good progress on my tasks."])
        elif message_type == "offtopic":
            options = offtopic_fallbacks.get(speaker, ["How's everyone doing today?"])
        else:  # technical
            options = technical_fallbacks.get(speaker, ["What do you think about our current approach?"])

        # Выбираем следующее сообщение по порядку
        counter = self.context["fallback_counters"][key]
        message = options[counter % len(options)]
        self.context["fallback_counters"][key] += 1

        return message

    async def analyze_message_with_ai(self, speaker: str, message: str) -> Dict[str, Any]:
        """AI анализ сообщения для определения типа и уместности"""
        if not self.gemini_client:
            return {"type": "unknown", "appropriate": True, "confidence": 0.5}

        prompt = f"""Analyze this standup meeting message for appropriateness and type:

Speaker: {speaker}
Message: "{message}"

STANDUP CONTEXT: Daily standups should focus on brief work updates, progress, and blockers.

Classify this message and respond with ONLY a JSON object:
{{
    "type": "standup_update|technical_discussion|off_topic|question|blocker",
    "appropriate_for_standup": true|false,
    "topic_detected": "brief description of main topic",
    "confidence": 0.0-1.0,
    "reason": "brief explanation"
}}

CLASSIFICATION GUIDELINES:
- standup_update: Work progress, completed tasks, current focus
- technical_discussion: Deep technical details, architecture discussions
- off_topic: Non-work topics, personal conversations
- question: Questions to team members
- blocker: Obstacles preventing progress

APPROPRIATENESS CRITERIA:
- Appropriate: Brief work updates, blockers, quick clarifications
- Not appropriate: Long technical discussions, off-topic chat, detailed questions"""

        try:
            response = await asyncio.to_thread(
                self.gemini_client.generate_content, prompt
            )

            if response and response.text:
                # Извлекаем JSON из ответа
                import json
                text = response.text.strip()
                # Ищем JSON в ответе
                start = text.find('{')
                end = text.rfind('}') + 1
                if start >= 0 and end > start:
                    json_str = text[start:end]
                    return json.loads(json_str)

        except Exception as e:
            self.print_status("AI analysis failed", str(e)[:30])

        # Fallback к простому анализу
        return self.simple_message_analysis(message)

    def simple_message_analysis(self, message: str) -> Dict[str, Any]:
        """Простой анализ как fallback"""
        message_lower = message.lower()

        # Off-topic keywords
        offtopic_words = ["coffee", "weekend", "game", "netflix", "restaurant", "weather", "movie", "sport", "vacation"]
        if any(word in message_lower for word in offtopic_words):
            return {
                "type": "off_topic",
                "appropriate_for_standup": False,
                "topic_detected": "personal/entertainment topic",
                "confidence": 0.8,
                "reason": "Contains off-topic keywords"
            }

        # Technical keywords
        technical_words = ["architecture", "database", "framework", "refactor", "testing", "api", "performance", "security"]
        if any(word in message_lower for word in technical_words) and len(message) > 100:
            return {
                "type": "technical_discussion",
                "appropriate_for_standup": False,
                "topic_detected": "technical deep-dive",
                "confidence": 0.7,
                "reason": "Detailed technical discussion"
            }

        # Technical questions
        if "?" in message and any(word in message_lower for word in ["what", "how", "why", "should"]):
            # Check if it's a technical question
            if any(word in message_lower for word in ["tool", "debug", "code", "api", "database", "performance", "architecture", "framework", "testing"]):
                return {
                    "type": "technical_discussion",
                    "appropriate_for_standup": False,
                    "topic_detected": "technical question",
                    "confidence": 0.8,
                    "reason": "Technical question to team"
                }
            else:
                return {
                    "type": "question",
                    "appropriate_for_standup": False,
                    "topic_detected": "question to team",
                    "confidence": 0.6,
                    "reason": "Contains question"
                }

        # Long messages
        if len(message) > 120:
            return {
                "type": "detailed_update",
                "appropriate_for_standup": False,
                "topic_detected": "verbose update",
                "confidence": 0.5,
                "reason": "Message too long for standup"
            }

        return {
            "type": "standup_update",
            "appropriate_for_standup": True,
            "topic_detected": "work update",
            "confidence": 0.6,
            "reason": "Appears to be appropriate standup content"
        }

    async def generate_moderator_response(self, speaker: str, message: str, analysis: Dict[str, Any]) -> str:
        """Генерация динамического ответа модератора через AI"""
        if not self.gemini_client:
            return self.get_fallback_moderator_response(speaker, analysis)

        message_type = analysis["type"]
        topic = analysis["topic_detected"]
        confidence = analysis["confidence"]

        # Контекст встречи
        meeting_context = ""
        if len(self.context["history"]) > 1:
            recent_speakers = [h[0] for h in self.context["history"][-3:]]
            meeting_context = f"Recent speakers: {', '.join(recent_speakers)}. "

        prompt = f"""You are an AI meeting moderator for a daily standup. A participant just said something inappropriate for standup format.

Participant: {speaker}
Their message: "{message}"
Issue detected: {message_type}
Topic: {topic}
Confidence: {confidence:.2f}

Meeting context: {meeting_context}This is a daily standup where people should give brief updates about work.

Generate a polite, professional moderator response that:
1. Acknowledges {speaker}'s contribution positively
2. Gently redirects the conversation back to standup format
3. Suggests appropriate next steps for the topic
4. Is natural and conversational (not robotic)
5. Is concise but complete (2-3 sentences maximum)
6. Uses {speaker}'s name

SPECIAL HANDLING FOR TECHNICAL DISCUSSIONS:
If this is a technical_discussion that has gone on for 3+ messages, suggest:
- "This sounds like a valuable technical discussion. Let's schedule a separate meeting to dive deeper into this."
- "Great technical insights! Let's move this to a dedicated technical session so we can give it proper time."
- "Important technical considerations! Let's create a follow-up meeting to explore this thoroughly."

Response types:
- For technical_discussion: Suggest creating a Jira ticket, scheduling a technical deep-dive, or moving to a separate discussion
- For off_topic: Gently redirect while acknowledging the team bonding aspect
- For question: Suggest addressing in a follow-up meeting or creating an action item
- For detailed_update: Thank for thoroughness, encourage brevity

Moderator suggestions:
- "Let's create a Jira ticket to track this technical discussion"
- "This sounds like a great topic for a separate technical session"
- "Should we schedule a deep-dive meeting for this?"
- "Let's add this to our action items for follow-up"

Respond with ONLY the moderator's message, no quotes or extra text."""

        try:
            self.print_status("Generating moderator response", f"For {message_type} from {speaker}")

            start_time = time.time()
            response = await asyncio.to_thread(
                self.gemini_client.generate_content, prompt
            )

            if response and response.text:
                moderator_response = response.text.strip()
                # Очищаем от лишних символов
                moderator_response = moderator_response.replace('"', '').replace("'", "'")
                # Увеличиваем лимит для более полных ответов модератора
                if len(moderator_response) > 400:
                    moderator_response = moderator_response[:400] + "..."

                duration = time.time() - start_time
                self.print_status("Moderator response generated", f"Duration: {duration:.2f}s")

                return moderator_response

        except Exception as e:
            self.print_status("Moderator generation failed", f"Using fallback: {str(e)[:30]}")

        # Fallback к шаблонным ответам
        return self.get_fallback_moderator_response(speaker, analysis)

    def get_fallback_moderator_response(self, speaker: str, analysis: Dict[str, Any]) -> str:
        """Fallback шаблонные ответы модератора"""
        message_type = analysis["type"]
        topic = analysis["topic_detected"]

        fallback_responses = {
            "off_topic": [
                f"That's great team bonding, {speaker}! Let's continue this conversation after our meeting.",
                f"Thanks for sharing, {speaker}! Let's save this discussion for our team chat.",
                f"I love the team connection, {speaker}! Let's refocus on our agenda for now."
            ],
            "technical_discussion": [
                f"Excellent technical point, {speaker}! Let's create a Jira ticket to track this discussion.",
                f"Great insight, {speaker}! Should we schedule a technical deep-dive session for this?",
                f"Important consideration, {speaker}! Let's add this to our action items for follow-up.",
                f"This sounds like a valuable discussion, {speaker}. Let's move this to a separate technical meeting."
            ],
            "question": [
                f"Good question, {speaker}! Let's address this after everyone shares their updates.",
                f"That's worth exploring, {speaker}! Let's circle back to this after standup.",
                f"Interesting question, {speaker}! Let's note this for discussion after updates."
            ],
            "detailed_update": [
                f"Thanks for the detailed update, {speaker}! Let's keep the remaining updates concise.",
                f"Great information, {speaker}! Let's continue with brief updates from everyone else.",
                f"Appreciate the thoroughness, {speaker}! Let's maintain our standup pace."
            ]
        }

        responses = fallback_responses.get(message_type, [f"Thanks, {speaker}! Let's continue with our standup."])
        return random.choice(responses)

    async def get_bot_response(self, speaker: str, message: str) -> str:
        # Используем AI анализ
        analysis = await self.analyze_message_with_ai(speaker, message)

        self.print_status("AI Analysis",
                         f"Type: {analysis['type']}, Appropriate: {analysis['appropriate_for_standup']}, "
                         f"Confidence: {analysis['confidence']:.2f}")

        # Обновляем счетчики сообщений
        self.update_message_counters(analysis['type'])

        # Проверяем нужно ли вмешательство на основе новой логики
        if not analysis["appropriate_for_standup"] and analysis["confidence"] > 0.6:
            should_intervene = self.should_moderator_intervene(analysis['type'])

            # Отладочная информация
            if analysis['type'] == "off_topic":
                print(f"{Colors.GRAY}   🔄 Off-topic counter: {self.context['consecutive_offtopic']}, Phase: {self.context['phase']}, Intervene: {should_intervene}{Colors.RESET}")
            elif analysis['type'] in ["technical_discussion", "technical", "question"]:
                print(f"{Colors.GRAY}   🔧 Technical counter: {self.context['consecutive_technical']}, Phase: {self.context['phase']}, Intervene: {should_intervene}{Colors.RESET}")

            if should_intervene:
                # Сбрасываем счетчики после вмешательства модератора
                self.context["consecutive_offtopic"] = 0
                self.context["consecutive_technical"] = 0
                intervention_type = "technical discussion" if analysis['type'] in ["technical_discussion", "technical"] else "off-topic"
                print(f"{Colors.GRAY}   🔄 Counters reset after moderator intervention ({intervention_type}){Colors.RESET}")

                # Генерируем динамический ответ модератора
                return await self.generate_moderator_response(speaker, message, analysis)

        # Если не нужно вмешиваться, возвращаем None
        return None

    async def generate_moderator_transition(self, next_speaker: str, previous_speaker: str = None) -> str:
        """Генерация перехода модератора к следующему участнику"""
        if not self.gemini_client:
            if previous_speaker:
                return f"Thank you, {previous_speaker}! {next_speaker}, you're up next."
            else:
                return f"{next_speaker}, please share your update."

        context = ""
        if previous_speaker:
            context = f"Previous speaker: {previous_speaker}. "

        if len(self.context["spoken"]) == 0:
            context += "This is the start of standup updates."
        elif len(self.context["spoken"]) >= len(self.participants):
            context += "All participants have given their standup updates."
        else:
            remaining = len(self.participants) - len(self.context["spoken"])
            context += f"{remaining} participants still need to give updates."

        prompt = f"""You are an AI meeting moderator for a daily standup. You need to transition to the next speaker.

Next speaker: {next_speaker}
Context: {context}

Generate a brief, natural transition that:
1. Thanks the previous speaker if there was one
2. Calls on {next_speaker} by name
3. Is professional but friendly
4. Keeps the meeting flowing smoothly
5. Is 1 sentence maximum



Respond with ONLY the moderator's transition, no quotes."""

        try:
            response = await asyncio.to_thread(
                self.gemini_client.generate_content, prompt
            )

            if response and response.text:
                transition = response.text.strip().replace('"', '')
                return transition

        except Exception as e:
            self.print_status("Transition generation failed", str(e)[:30])

        # Fallback
        if previous_speaker:
            return f"Thank you, {previous_speaker}! {next_speaker}, you're up next."
        else:
            return f"{next_speaker}, please share your update."

    async def generate_moderator_greeting(self, first_speaker: str) -> str:
        """Generate dynamic moderator greeting"""
        participants_list = list(self.participants.keys())

        prompt = f"""You are an AI meeting moderator starting a daily standup meeting.

CONTEXT:
- Team members present: {', '.join(participants_list)}
- First speaker: {first_speaker}
- This is the beginning of the standup meeting

Generate a natural, professional greeting that:
1. Welcomes the team to the standup
2. Sets a positive, productive tone
3. Calls on {first_speaker} to begin
4. Feels authentic and not templated
5. Is 1-2 sentences maximum

AVOID:
- Generic phrases like "Good morning everyone"
- Repetitive patterns
- Overly formal language
- Templated responses

Generate a fresh, natural moderator greeting:"""

        try:
            response = await asyncio.to_thread(
                self.gemini_client.generate_content, prompt
            )

            if response and response.text:
                greeting = response.text.strip().replace('"', '')
                return greeting
            else:
                return f"Let's get started with our standup. {first_speaker}, please begin with your update."

        except Exception as e:
            print(f"Error generating moderator greeting: {e}")
            return f"Good morning! Let's start our standup. {first_speaker}, please begin with your update."

    def select_next_speaker(self) -> str:
        """Выбор следующего участника для standup"""
        # В standup порядке - сначала те, кто не говорил
        not_spoken = [name for name in self.participants.keys()
                     if name not in self.context["spoken"]]

        if not_spoken:
            return not_spoken[0]  # Первый из не говоривших
        else:
            # Все дали обновления - возвращаем None чтобы завершить standup
            return None

    def should_continue_standup(self) -> bool:
        """Проверка, должен ли standup продолжаться"""
        # Продолжаем пока не все дали обновления
        return len(self.context["spoken"]) < len(self.participants)

    def get_random_speaker_for_discussion(self) -> str:
        """Выбор случайного участника для дискуссии после standup"""
        return random.choice(list(self.participants.keys()))

    def extract_addressed_participant(self, message: str) -> str:
        """Extract participant name if someone is directly addressed in the message"""
        message_lower = message.lower()

        # Ищем обращения к участникам по имени
        for participant_name in self.participants.keys():
            name_lower = participant_name.lower()

            # Проверяем различные паттерны обращения
            patterns = [
                f"{name_lower},",  # "Alice,"
                f"{name_lower}?",  # "Alice?"
                f"{name_lower} ",  # "Alice "
                f" {name_lower},", # " Alice,"
                f" {name_lower}?", # " Alice?"
                f" {name_lower} ", # " Alice "
            ]

            for pattern in patterns:
                if pattern in message_lower:
                    return participant_name

        return None

    async def detect_jira_ticket_suggestion(self, message: str) -> dict:
        """Detect if message suggests creating a Jira ticket using AI analysis"""
        if not self.gemini_client:
            return {"detected": False}

        prompt = f"""Analyze this meeting message to determine if the speaker is suggesting to create a Jira ticket, issue, or task to track work.

MESSAGE: "{message}"

Determine if this message suggests:
1. Creating a Jira ticket/issue/task
2. Tracking work in a backlog
3. Filing something for later work
4. Creating documentation for follow-up
5. Assigning work to be tracked

IMPORTANT: Only detect SUGGESTIONS to create tickets, not:
- Regular work updates
- Technical discussions without action items
- Questions without follow-up requests
- General comments

Respond with JSON format:
{{
    "detected": true/false,
    "confidence": 0.0-1.0,
    "reasoning": "brief explanation",
    "suggested_title": "brief ticket title if detected",
    "work_type": "bug_fix|feature|optimization|investigation|documentation|other",
    "suggestion_type": "self_assign|moderator_create|team_discussion"
}}

Examples of ticket suggestions:

SELF_ASSIGN (speaker will create/handle the ticket themselves):
- "I'll open a ticket for this performance issue"
- "Will create a JIRA for the API bug"
- "I'll file this as a bug report"

MODERATOR_CREATE (asking moderator/team to create ticket):
- "We should create a ticket for this performance issue"
- "Can someone file an issue for the API bug?"
- "Let's track this in our backlog"

TEAM_DISCUSSION (general suggestion without specific assignment):
- "We need to document this for later"
- "This should be tracked somewhere"

Examples of NOT ticket suggestions:
- "I'm working on the API today"
- "The performance looks good"
- "What do you think about this approach?"

Analyze the message:"""

        try:
            response = await asyncio.to_thread(
                self.gemini_client.generate_content, prompt
            )

            if response and response.text:
                import json
                try:
                    # Extract JSON from response
                    response_text = response.text.strip()
                    if response_text.startswith('```json'):
                        response_text = response_text.split('```json')[1].split('```')[0]
                    elif response_text.startswith('```'):
                        response_text = response_text.split('```')[1].split('```')[0]

                    analysis = json.loads(response_text)

                    if analysis.get("detected", False) and analysis.get("confidence", 0) > 0.7:
                        # AI detected a ticket suggestion with high confidence
                        title = analysis.get("suggested_title", self.extract_ticket_title(message))
                        suggestion_type = analysis.get("suggestion_type", "moderator_create")

                        # Для self_assign не нужно назначать, для остальных - предлагаем
                        assignee = None if suggestion_type == "self_assign" else self.suggest_ticket_assignee(message)

                        return {
                            "detected": True,
                            "title": title,
                            "suggested_assignee": assignee,
                            "suggestion_type": suggestion_type,
                            "original_message": message,
                            "confidence": analysis.get("confidence", 0.8),
                            "work_type": analysis.get("work_type", "other"),
                            "reasoning": analysis.get("reasoning", "AI detected ticket suggestion")
                        }

                except json.JSONDecodeError:
                    # Fallback if JSON parsing fails
                    pass

        except Exception as e:
            print(f"Error in AI ticket detection: {e}")

        return {"detected": False}

    async def detect_jira_assignment_confirmation(self, speaker: str, message: str) -> dict:
        """Detect if message confirms or declines Jira ticket assignment"""
        if not self.context["waiting_for_confirmation"]:
            return {"detected": False}

        pending = self.context["pending_jira_assignment"]
        if not pending or speaker != pending["assignee"]:
            return {"detected": False}

        if not self.gemini_client:
            # Simple keyword fallback
            message_lower = message.lower()
            if any(word in message_lower for word in ["yes", "sure", "okay", "ok", "fine", "agree"]):
                return {"detected": True, "confirmed": True}
            elif any(word in message_lower for word in ["no", "can't", "cannot", "busy", "decline"]):
                return {"detected": True, "confirmed": False}
            return {"detected": False}

        prompt = f"""Analyze this message to determine if the speaker is confirming or declining a Jira ticket assignment.

CONTEXT: {speaker} was asked to take on a Jira ticket titled "{pending['title']}"

MESSAGE: "{message}"

Determine if this message:
1. Confirms acceptance of the ticket assignment
2. Declines the ticket assignment
3. Is unrelated to the assignment question

Respond with JSON format:
{{
    "detected": true/false,
    "confirmed": true/false/null,
    "confidence": 0.0-1.0,
    "reasoning": "brief explanation"
}}

Examples of confirmation:
- "Yes, I can take that"
- "Sure, I'll handle it"
- "Okay, assign it to me"

Examples of decline:
- "No, I'm too busy"
- "Can someone else take it?"
- "I can't handle that right now"

Examples of unrelated:
- "What about the API performance?"
- "I think we should also consider..."

Analyze the message:"""

        try:
            response = await asyncio.to_thread(
                self.gemini_client.generate_content, prompt
            )

            if response and response.text:
                import json
                try:
                    response_text = response.text.strip()
                    if response_text.startswith('```json'):
                        response_text = response_text.split('```json')[1].split('```')[0]
                    elif response_text.startswith('```'):
                        response_text = response_text.split('```')[1].split('```')[0]

                    analysis = json.loads(response_text)

                    if analysis.get("detected", False) and analysis.get("confidence", 0) > 0.7:
                        return {
                            "detected": True,
                            "confirmed": analysis.get("confirmed", None),
                            "confidence": analysis.get("confidence", 0.8),
                            "reasoning": analysis.get("reasoning", "AI detected assignment response")
                        }

                except json.JSONDecodeError:
                    pass

        except Exception as e:
            print(f"Error in AI assignment confirmation detection: {e}")

        return {"detected": False}

    def build_conversation_context(self, current_speaker: str) -> str:
        """Build rich conversation context for message generation"""
        if not self.context["history"]:
            return "This is the start of the conversation."

        # Get last 2-3 messages for context
        recent_messages = self.context["history"][-3:]
        context_parts = []

        # Check if current speaker was directly addressed
        addressed_context = ""
        if len(recent_messages) > 0:
            last_speaker, last_message = recent_messages[-1]
            if current_speaker.lower() in last_message.lower():
                addressed_context = f"\n\nIMPORTANT: {last_speaker} just addressed you directly in their message. You should respond to their question/comment."

        # Build conversation flow
        if len(recent_messages) == 1:
            speaker, message = recent_messages[0]
            context_parts.append(f"Previous message: {speaker} said '{message[:100]}{'...' if len(message) > 100 else ''}'")
        elif len(recent_messages) >= 2:
            context_parts.append("Recent conversation:")
            for speaker, message in recent_messages:
                truncated = message[:80] + "..." if len(message) > 80 else message
                context_parts.append(f"  {speaker}: '{truncated}'")

        # Add topic continuity hint
        if len(recent_messages) > 0:
            last_speaker, last_message = recent_messages[-1]
            if any(word in last_message.lower() for word in ["tools", "plugins", "debugging", "performance", "api", "testing"]):
                context_parts.append(f"\nCONTEXT: The conversation is about technical topics. Try to build on or respond to what {last_speaker} mentioned.")

        return "\n".join(context_parts) + addressed_context

    async def generate_jira_confirmation_response(self, speaker: str) -> str:
        """Generate response to Jira ticket assignment confirmation request"""
        pending = self.context["pending_jira_assignment"]
        participant = self.participants[speaker]

        if not self.gemini_client:
            # Simple fallback responses
            responses = [
                "Yes, I can take that on.",
                "Sure, I'll handle it.",
                "Okay, assign it to me.",
                "Sounds good, I'll take care of it."
            ]
            return random.choice(responses)

        prompt = f"""You are {speaker}, a {participant['expertise']} developer with a {participant['style']} communication style.

CONTEXT: The meeting moderator just asked you to confirm if you're okay with being assigned a Jira ticket.

TICKET DETAILS:
- Title: "{pending['title']}"
- Suggested by: {pending['original_speaker']}
- You are being asked to take ownership of this work

Generate a natural response that either:
1. ACCEPTS the assignment (most common)
2. DECLINES politely if you're too busy/not the right person

GUIDELINES:
- Use your {participant['style']} communication style
- Be professional but natural
- Keep it brief (1-2 sentences)
- Show willingness to help the team

EXAMPLES OF ACCEPTANCE:
- "Yes, I can take that on."
- "Sure, I'll handle it."
- "Absolutely, I'll get that sorted."
- "No problem, I'll take care of it."

EXAMPLES OF DECLINE:
- "I'm swamped this sprint, can someone else take it?"
- "Actually, I think [other person] would be better for this."
- "I'm tied up with the API work, maybe Bob could handle it?"

Generate your response as {speaker}:"""

        try:
            response = await asyncio.to_thread(
                self.gemini_client.generate_content, prompt
            )

            if response and response.text:
                return response.text.strip().replace('"', '')
            else:
                return "Yes, I can take that on."

        except Exception as e:
            print(f"Error generating Jira confirmation response: {e}")
            return "Sure, I'll handle it."

    def extract_ticket_title(self, message: str) -> str:
        """Extract potential ticket title from message"""
        # Простая эвристика для извлечения темы
        message_clean = message.replace('"', '').replace("'", "")

        # Ищем технические термины и проблемы
        if "performance" in message.lower():
            return "Performance optimization task"
        elif "bug" in message.lower() or "issue" in message.lower():
            return "Bug fix investigation"
        elif "refactor" in message.lower():
            return "Code refactoring task"
        elif "test" in message.lower():
            return "Testing implementation"
        elif "api" in message.lower():
            return "API development task"
        elif "database" in message.lower() or "db" in message.lower():
            return "Database optimization"
        elif "frontend" in message.lower() or "ui" in message.lower():
            return "Frontend development task"
        elif "backend" in message.lower():
            return "Backend development task"
        else:
            return "Development task from discussion"

    def suggest_ticket_assignee(self, message: str) -> str:
        """Suggest who should be assigned to the ticket based on context"""
        message_lower = message.lower()

        # Проверяем упоминания участников в сообщении
        for participant_name in self.participants.keys():
            if participant_name.lower() in message_lower:
                return participant_name

        # Назначаем на основе экспертизы
        if any(word in message_lower for word in ["frontend", "ui", "react", "component"]):
            # Ищем frontend разработчика
            for name, participant in self.participants.items():
                if participant.get("expertise") == "frontend":
                    return name

        elif any(word in message_lower for word in ["backend", "api", "database", "server"]):
            # Ищем backend разработчика
            for name, participant in self.participants.items():
                if participant.get("expertise") == "backend":
                    return name

        elif any(word in message_lower for word in ["devops", "deployment", "infrastructure", "pipeline", "ci/cd"]):
            # Ищем DevOps инженера
            for name, participant in self.participants.items():
                if participant.get("expertise") == "devops":
                    return name
            # Если DevOps нет, назначаем на fullstack разработчика
            for name, participant in self.participants.items():
                if participant.get("expertise") == "fullstack":
                    return name

        # По умолчанию - случайный участник
        return random.choice(list(self.participants.keys()))

    async def generate_discussion_transition(self) -> str:
        """Generate dynamic transition to discussion phase"""
        participants_list = list(self.participants.keys())

        prompt = f"""You are an AI meeting moderator transitioning from standup updates to open discussion.

CONTEXT:
- All team members ({', '.join(participants_list)}) have given their standup updates
- Now opening the floor for questions, discussions, or additional topics
- Want to encourage team collaboration and knowledge sharing

Generate a natural transition that:
1. Acknowledges the completed standup updates
2. Opens the floor for discussion
3. Encourages questions or additional topics
4. Maintains positive energy
5. Is 1-2 sentences maximum

AVOID:
- Generic phrases like "Great updates everyone"
- Repetitive patterns
- Overly formal language
- Templated responses

Generate a fresh, natural discussion transition:"""

        try:
            response = await asyncio.to_thread(
                self.gemini_client.generate_content, prompt
            )

            if response and response.text:
                transition = response.text.strip().replace('"', '')
                return transition
            else:
                return "Thanks for the updates! Now let's open the floor for any questions or discussions."

        except Exception as e:
            print(f"Error generating discussion transition: {e}")
            return "Great standup updates everyone! Now let's open the floor for any questions, discussions, or additional topics."

    async def generate_meeting_conclusion(self) -> str:
        """Generate dynamic meeting conclusion"""
        participants_list = list(self.participants.keys())
        meeting_duration = (datetime.now() - self.start_time).total_seconds() / 60

        prompt = f"""You are an AI meeting moderator concluding a daily standup meeting.

CONTEXT:
- Team members: {', '.join(participants_list)}
- Meeting duration: {meeting_duration:.1f} minutes
- {self.message_count} messages exchanged
- Standup and discussion phases completed

Generate a natural conclusion that:
1. Thanks the team for their participation
2. Acknowledges the productive meeting
3. Provides closure to the session
4. Maintains positive team energy
5. Is 1-2 sentences maximum

AVOID:
- Generic phrases like "Great job everyone"
- Repetitive patterns
- Overly formal language
- Templated responses

Generate a fresh, natural meeting conclusion:"""

        try:
            response = await asyncio.to_thread(
                self.gemini_client.generate_content, prompt
            )

            if response and response.text:
                conclusion = response.text.strip().replace('"', '')
                return conclusion
            else:
                return "Thanks everyone for a productive standup! Have a great day."

        except Exception as e:
            print(f"Error generating meeting conclusion: {e}")
            return "Great job everyone! Thanks for the productive standup."

    async def generate_jira_ticket_response(self, speaker: str, ticket_info: dict) -> str:
        """Generate moderator response for Jira ticket creation"""
        title = ticket_info["title"]
        assignee = ticket_info["suggested_assignee"]
        suggestion_type = ticket_info.get("suggestion_type", "moderator_create")

        if suggestion_type == "self_assign":
            # Speaker will handle it themselves
            prompt = f"""You are an AI meeting moderator responding to someone who said they will create a Jira ticket themselves.

CONTEXT:
- {speaker} said they will create/open a ticket themselves
- Ticket topic: "{title}"
- Original message: "{ticket_info['original_message']}"

Generate a professional moderator response that:
1. Acknowledges their initiative
2. Thanks them for taking ownership
3. Briefly mentions the topic/issue
4. Keeps it concise and supportive
5. Does NOT offer to create the ticket (they're doing it)
6. Does NOT assign it to anyone (they're handling it)


Generate a natural moderator response:"""

        else:
            # Moderator creates and assigns
            prompt = f"""You are an AI meeting moderator responding to a suggestion to create a Jira ticket.

CONTEXT:
- {speaker} suggested creating a ticket
- Suggested title: "{title}"
- Suggested assignee: {assignee}
- Suggestion type: {suggestion_type}

Generate a professional moderator response that:
1. Acknowledges the good suggestion
2. States you'll create the Jira ticket
3. Mentions the brief title
4. States who you suggest to assign it to
5. Asks that person if they agree to be assigned
6. Keeps it concise and professional

EXAMPLE STRUCTURE:
"Great suggestion, [speaker]! I'll create a Jira ticket titled '[title]' and suggest assigning it to [assignee]. [assignee], are you okay with taking this on?"

Generate a natural moderator response:"""

        try:
            response = await asyncio.to_thread(
                self.gemini_client.generate_content, prompt
            )

            if response and response.text:
                return response.text.strip().replace('"', '')
            else:
                return f"Great suggestion, {speaker}! I'll create a Jira ticket titled '{title}' and assign it to {assignee}. {assignee}, are you okay with taking this on?"

        except Exception as e:
            print(f"Error generating Jira response: {e}")
            return f"Good idea, {speaker}! I'll create a ticket '{title}' for {assignee}. {assignee}, does that work for you?"

    async def run_quick_demo(self, max_messages: int = 8):
        if not await self.initialize():
            return
        
        print(f"\n{Colors.YELLOW}🎬 Starting quick dynamic demo...{Colors.RESET}")
        print(f"{Colors.CYAN}📋 Max {max_messages} messages with AI generation{Colors.RESET}")
        
        # Приветствие и первый переход
        first_speaker = self.select_next_speaker()
        greeting = await self.generate_moderator_greeting(first_speaker)
        self.print_message("MODERATOR", greeting)
        await self.generate_voice("MODERATOR", greeting)

        # Основной цикл с управлением фазами встречи
        previous_speaker = None
        current_speaker = first_speaker
        standup_phase = True
        addressed_participant = None  # Кто был упомянут в предыдущем сообщении

        for i in range(max_messages):
            # Проверяем фазу встречи
            if standup_phase and not self.should_continue_standup() and not self.context["waiting_for_confirmation"]:
                # Все дали standup обновления и не ожидаем подтверждения, переходим к дискуссии
                standup_phase = False
                self.context["phase"] = "discussion"  # Обновляем фазу в контексте
                self.context["consecutive_offtopic"] = 0  # Сбрасываем счетчик
                print(f"\n{Colors.YELLOW}🔄 Transitioning to discussion phase{Colors.RESET}")

                # Модератор объявляет переход к дискуссии
                discussion_transition = await self.generate_discussion_transition()
                self.print_message("MODERATOR", discussion_transition)
                await self.generate_voice("MODERATOR", discussion_transition)

                current_speaker = self.get_random_speaker_for_discussion()

            # Выбираем участника
            if i == 0:
                speaker = current_speaker
            else:
                if standup_phase:
                    # В фазе standup - проверяем ожидание подтверждения
                    if self.context["waiting_for_confirmation"]:
                        # Если ожидаем подтверждения, следующим должен говорить назначаемый
                        speaker = self.context["pending_jira_assignment"]["assignee"]
                        print(f"{Colors.CYAN}   ⏳ {speaker} should respond to Jira assignment{Colors.RESET}")
                    else:
                        # Выбираем следующего кто не говорил
                        next_speaker = self.select_next_speaker()
                        if next_speaker is None:
                            # Все дали обновления, завершаем standup
                            standup_phase = False
                            if i < max_messages - 1:  # Если есть место для дискуссии
                                speaker = self.get_random_speaker_for_discussion()
                            else:
                                break
                        else:
                            speaker = next_speaker
                else:
                    # В фазе дискуссии - проверяем приоритеты
                    if self.context["waiting_for_confirmation"]:
                        # Если ожидаем подтверждения, следующим должен говорить назначаемый
                        speaker = self.context["pending_jira_assignment"]["assignee"]
                        print(f"{Colors.CYAN}   ⏳ {speaker} should respond to Jira assignment{Colors.RESET}")
                    elif addressed_participant:
                        # Если кто-то был упомянут, он должен ответить
                        speaker = addressed_participant
                        addressed_participant = None  # Сбрасываем после использования
                        print(f"{Colors.YELLOW}   👤 {speaker} responds to direct address{Colors.RESET}")
                    else:
                        # Случайный выбор
                        speaker = self.get_random_speaker_for_discussion()

                # Модератор управляет переходами только в standup фазе и если не ожидает подтверждения
                if standup_phase and not self.context["waiting_for_confirmation"]:
                    transition = await self.generate_moderator_transition(speaker, previous_speaker)
                    self.print_message("MODERATOR", transition)
                    await self.generate_voice("MODERATOR", transition)
                # В фазе дискуссии участники говорят спонтанно без вызова модератора

            # Генерируем сообщение участника
            message = await self.generate_message(speaker, standup_phase)

            # Показываем сообщение участника
            self.print_message(speaker, message)
            await self.generate_voice(speaker, message)

            # В фазе дискуссии проверяем обращения к участникам
            if not standup_phase:
                addressed_participant = self.extract_addressed_participant(message)
                if addressed_participant:
                    print(f"{Colors.CYAN}   📞 {speaker} addressed {addressed_participant}{Colors.RESET}")

            # Сначала проверяем подтверждение назначения Jira тикета
            assignment_confirmation = await self.detect_jira_assignment_confirmation(speaker, message)
            if assignment_confirmation["detected"]:
                pending = self.context["pending_jira_assignment"]
                if assignment_confirmation["confirmed"]:
                    print(f"{Colors.GREEN}   ✅ {speaker} confirmed Jira ticket assignment{Colors.RESET}")
                    confirmation_response = f"Perfect! I've assigned the ticket '{pending['title']}' to {speaker}. Thanks for taking this on!"
                else:
                    print(f"{Colors.YELLOW}   ❌ {speaker} declined Jira ticket assignment{Colors.RESET}")
                    confirmation_response = f"No problem, {speaker}! Let me reassign this ticket to someone else. I'll follow up on the assignment later."

                # Сбрасываем состояние ожидания
                self.context["pending_jira_assignment"] = None
                self.context["waiting_for_confirmation"] = False

                # Модератор отвечает на подтверждение
                self.print_message("MODERATOR", confirmation_response)
                await self.generate_voice("MODERATOR", confirmation_response)
                self.print_status("Assignment confirmation", f"{speaker} {'accepted' if assignment_confirmation['confirmed'] else 'declined'} ticket")

            # Если не ожидаем подтверждения, проверяем предложения о создании Jira тикетов
            elif not self.context["waiting_for_confirmation"]:
                jira_suggestion = await self.detect_jira_ticket_suggestion(message)
                if jira_suggestion["detected"]:
                    suggestion_type = jira_suggestion.get("suggestion_type", "moderator_create")
                    print(f"{Colors.YELLOW}   🎫 Jira ticket suggestion detected (confidence: {jira_suggestion.get('confidence', 0):.2f}, type: {suggestion_type}){Colors.RESET}")
                    print(f"{Colors.GRAY}   📝 Reasoning: {jira_suggestion.get('reasoning', 'N/A')}{Colors.RESET}")

                    # Сбрасываем счетчик off-topic после Jira предложения
                    self.context["consecutive_offtopic"] = 0
                    print(f"{Colors.GRAY}   🔄 Off-topic counter reset after Jira suggestion{Colors.RESET}")

                    if suggestion_type == "self_assign":
                        # Участник сам создаст тикет - только подтверждаем
                        jira_response = await self.generate_jira_ticket_response(speaker, jira_suggestion)
                        self.print_message("MODERATOR", jira_response)
                        await self.generate_voice("MODERATOR", jira_response)
                        self.print_status("Jira self-assignment", f"{speaker} will handle: {jira_suggestion['title']}")
                    else:
                        # Модератор создает и назначает - ожидаем подтверждения
                        self.context["pending_jira_assignment"] = {
                            "title": jira_suggestion['title'],
                            "assignee": jira_suggestion['suggested_assignee'],
                            "original_speaker": speaker
                        }
                        self.context["waiting_for_confirmation"] = True

                        jira_response = await self.generate_jira_ticket_response(speaker, jira_suggestion)
                        self.print_message("MODERATOR", jira_response)
                        await self.generate_voice("MODERATOR", jira_response)
                        self.print_status("Jira ticket creation", f"Title: {jira_suggestion['title']}, Assignee: {jira_suggestion['suggested_assignee']}")
                        print(f"{Colors.CYAN}   ⏳ Waiting for {jira_suggestion['suggested_assignee']} to confirm assignment{Colors.RESET}")
                else:
                    # Проверяем нужно ли обычное вмешательство модератора
                    bot_response = await self.get_bot_response(speaker, message)
                    if bot_response:
                        self.print_message("MODERATOR", bot_response)
                        await self.generate_voice("MODERATOR", bot_response)
                        self.print_status("Bot intervention", f"Responded to {speaker}")

            # В standup фазе отмечаем что участник дал обновление
            if standup_phase:
                self.context["spoken"].add(speaker)
                print(f"{Colors.GRAY}   ✅ {speaker} completed standup update ({len(self.context['spoken'])}/{len(self.participants)}){Colors.RESET}")

            previous_speaker = speaker
            self.message_count += 1

            # Показываем прогресс
            if (i + 1) % 3 == 0:
                elapsed = (datetime.now() - self.start_time).total_seconds() / 60
                phase_text = "Standup" if standup_phase else "Discussion"
                print(f"\n{Colors.GRAY}📊 Progress: {i+1}/{max_messages} messages, {elapsed:.1f}min, Phase: {phase_text}{Colors.RESET}")
        
        # Завершение
        conclusion = await self.generate_meeting_conclusion()
        # Пауза убрана для быстрого демо
        self.print_message("MODERATOR", conclusion)
        await self.generate_voice("MODERATOR", conclusion)
        
        # Итоги
        duration = (datetime.now() - self.start_time).total_seconds() / 60
        print(f"\n{Colors.GREEN}🎉 Quick demo completed!{Colors.RESET}")
        print(f"{Colors.CYAN}📊 {self.message_count} messages in {duration:.1f} minutes{Colors.RESET}")
        print(f"{Colors.CYAN}🤖 All dialogue generated dynamically by AI{Colors.RESET}")

def main():
    # Check if first argument is a config name
    config_name = None
    config = None

    if len(sys.argv) > 1 and not sys.argv[1].isdigit():
        # First argument is config name
        config_name = sys.argv[1]

        try:
            loader = MeetingConfigLoader()
            config = loader.get_config(config_name)

            if config is None:
                print(f"Configuration '{config_name}' not found.")
                print("Available configurations:")
                for name in loader.list_configs():
                    info = loader.get_config_info(name)
                    print(f"  {name}: {info['name']} ({info['num_participants']} participants)")
                return

            print(f"{Colors.CYAN}🎛️ Using Configuration: {config.name}{Colors.RESET}")
            print(f"  📝 Description: {config.description}")
            print(f"  📊 Max messages: {config.max_messages}")
            print(f"  👥 Participants: {len(config.participants)}")
            print(f"  💬 Off-topic tendency: {config.offtopic_percent:.1%}")
            print(f"  🔧 Technical discussion tendency: {config.technical_percent:.1%}")
            print()

            # Create demo with configuration
            demo = QuickDynamicDemo(config=config)
            asyncio.run(demo.run_quick_demo(config.max_messages))

        except Exception as e:
            print(f"Error loading configuration: {e}")
            return

    else:
        # Parse command line arguments for manual configuration
        max_messages = 6  # Default
        offtopic_percent = 0.3  # Default 30%
        technical_percent = 0.4  # Default 40%
        num_participants = 3  # Default 3 participants

        if len(sys.argv) > 1:
            try:
                max_messages = int(sys.argv[1])
            except ValueError:
                print("Usage:")
                print("  python quick_dynamic_demo.py [config_name]")
                print("  python quick_dynamic_demo.py [max_messages] [offtopic_percent] [technical_percent] [num_participants]")
                print()
                print("Examples:")
                print("  python quick_dynamic_demo.py default")
                print("  python quick_dynamic_demo.py large_team")
                print("  python quick_dynamic_demo.py 8 0.5 0.6 4")
                print()
                print("Available configurations:")
                try:
                    loader = MeetingConfigLoader()
                    for name in loader.list_configs():
                        info = loader.get_config_info(name)
                        print(f"  {name}: {info['name']} ({info['num_participants']} participants)")
                except:
                    print("  (Error loading configurations)")
                return

        if len(sys.argv) > 2:
            try:
                offtopic_percent = float(sys.argv[2])
            except ValueError:
                print("Invalid offtopic_percent. Using default 0.3")

        if len(sys.argv) > 3:
            try:
                technical_percent = float(sys.argv[3])
            except ValueError:
                print("Invalid technical_percent. Using default 0.4")

        if len(sys.argv) > 4:
            try:
                num_participants = int(sys.argv[4])
            except ValueError:
                print("Invalid num_participants. Using default 3")

        # Create demo with custom parameters
        demo = QuickDynamicDemo(offtopic_percent=offtopic_percent,
                               technical_percent=technical_percent,
                               num_participants=num_participants)

        print(f"{Colors.CYAN}🎛️ Meeting Parameters:{Colors.RESET}")
        print(f"  📊 Max messages: {max_messages}")
        print(f"  👥 Participants: {num_participants}")
        print(f"  💬 Off-topic tendency: {offtopic_percent:.1%}")
        print(f"  🔧 Technical discussion tendency: {technical_percent:.1%}")
        print()

        asyncio.run(demo.run_quick_demo(max_messages))

if __name__ == "__main__":
    main()
